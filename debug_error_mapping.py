#!/usr/bin/env python3
"""
调试错误码映射问题
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from svc.core.exceptions.error_codes import ErrorCode
from svc.core.exceptions.route_error_handler import (PRODUCT_ERROR_MAPPING,
                                                     ResultHandler)
from svc.core.models.result import ResultFactory


def debug_error_mapping():
    print("🔍 调试错误码映射问题")
    print("=" * 50)
    
    # 创建一个资源不存在的结果
    result = ResultFactory.resource_not_found('product', 1)
    print(f"Result code: {result.result_code}, type: {type(result.result_code)}")
    print(f"Result message: {result.result_msg}")
    print(f"Result success: {result.is_success}")
    
    print("\n📋 错误映射信息:")
    print(f"Mapping keys (前5个): {list(PRODUCT_ERROR_MAPPING.keys())[:5]}")
    print(f"ErrorCode.NOT_FOUND: {ErrorCode.NOT_FOUND}")
    print(f"str(ErrorCode.NOT_FOUND): {str(ErrorCode.NOT_FOUND)}")
    print(f"ErrorCode.NOT_FOUND.value: {ErrorCode.NOT_FOUND.value}")
    
    print("\n🔍 映射查找测试:")
    print(f"Direct lookup (result_code in mapping): {result.result_code in PRODUCT_ERROR_MAPPING}")
    print(f"String lookup (str(ErrorCode.NOT_FOUND) in mapping): {str(ErrorCode.NOT_FOUND) in PRODUCT_ERROR_MAPPING}")
    
    # 测试枚举转换
    print("\n🔄 枚举转换测试:")
    found_enum = None
    for error_code in ErrorCode:
        if error_code.value == result.result_code:
            found_enum = error_code
            error_code_str = str(error_code)
            print(f"Found matching enum: {error_code_str}")
            print(f"Enum string in mapping: {error_code_str in PRODUCT_ERROR_MAPPING}")
            if error_code_str in PRODUCT_ERROR_MAPPING:
                print(f"Mapped status code: {PRODUCT_ERROR_MAPPING[error_code_str]}")
            break
    
    if not found_enum:
        print("❌ 没有找到匹配的枚举")
    
    # 测试ResultHandler
    print("\n🎯 ResultHandler测试:")
    handler = ResultHandler(PRODUCT_ERROR_MAPPING)
    
    try:
        # 模拟ResultHandler的逻辑
        status_code = 400  # 默认状态码
        
        if handler.error_mapping and result.result_code is not None:
            # 尝试直接匹配
            if result.result_code in handler.error_mapping:
                status_code = handler.error_mapping[result.result_code]
                print(f"✅ 直接匹配成功，状态码: {status_code}")
            else:
                print("❌ 直接匹配失败，尝试枚举转换...")
                # 尝试将整数错误码转换为ErrorCode枚举字符串进行匹配
                if isinstance(result.result_code, int):
                    for error_code in ErrorCode:
                        # ErrorCode的value可能是字符串，需要转换比较
                        error_value = error_code.value
                        if isinstance(error_value, str):
                            try:
                                error_value = int(error_value)
                            except (ValueError, TypeError):
                                continue

                        if error_value == result.result_code:
                            error_code_str = str(error_code)
                            print(f"找到匹配枚举: {error_code_str}")
                            if error_code_str in handler.error_mapping:
                                status_code = handler.error_mapping[error_code_str]
                                print(f"✅ 枚举匹配成功，状态码: {status_code}")
                            else:
                                print(f"❌ 枚举不在映射中")
                            break
        
        print(f"最终状态码: {status_code}")
        
    except Exception as e:
        print(f"❌ ResultHandler测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_error_mapping()
