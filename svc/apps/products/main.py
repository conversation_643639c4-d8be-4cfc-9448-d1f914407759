"""
商品管理模块主路由文件

该文件将所有商品管理相关的路由组合在一起，
包括商品、分类、库存等子模块的路由。
"""
from fastapi import APIRouter

from svc.apps.products.routers.category import router as category_router
from svc.apps.products.routers.inventory import router as inventory_router
from svc.apps.products.routers.product import router as product_router
from svc.apps.products.routers.sku import router as sku_router
from svc.apps.products.routers.spec import router as spec_router

# 创建主路由器
router = APIRouter(
    prefix="/products",
    tags=["商品管理"]
)

# 包含所有子路由
router.include_router(
    product_router,
    prefix="",
    tags=["商品"]
)

router.include_router(
    category_router,
    prefix="/categories",
    tags=["商品分类"]
)

router.include_router(
    inventory_router,
    prefix="/inventory",
    tags=["库存管理"]
)

router.include_router(
    spec_router,
    prefix="/specs",
    tags=["商品规格"]
)

router.include_router(
    sku_router,
    prefix="/skus",
    tags=["商品SKU"]
)

__all__ = ["router"]
