from typing import Any, Dict, List, Optional

from fastapi_events.dispatcher import dispatch
from redis.asyncio import Redis

from svc.apps.products.models.sku import ProductSKU
from svc.apps.products.repositories.sku import ProductSKURepository
from svc.apps.products.repositories.spec import (SpecOptionRepository,
                                                 SpecRepository)
from svc.apps.products.schemas.product import (ProductSKUCreate,
                                               ProductSKUListResponse,
                                               ProductSKUResponse,
                                               ProductSKUUpdate)
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.models.result import Result
from svc.core.services.base import BaseService
from svc.core.services.mixins.batch_operation import BatchOperationMixin
from svc.core.services.mixins.cache import CacheMixin
from svc.core.services.mixins.error_result import ErrorResultMixin


class ProductSKUService(BaseService, ErrorResultMixin, BatchOperationMixin, CacheMixin):
    """SKU服务类，提供SKU的创建、查询和管理功能

    该服务类负责：
    1. SKU的创建和管理
    2. SKU状态更新
    3. SKU库存管理
    4. SKU与规格选项的关联管理

    服务类依赖ProductSKURepository进行数据访问，
    实现业务逻辑与数据访问的分离。
    """

    # 设置资源类型名称
    resource_type = "sku"

    def __init__(
        self,
        redis: Optional[Redis] = None,
        sku_repo: Optional[ProductSKURepository] = None,
        spec_repo: Optional[SpecRepository] = None,
        spec_option_repo: Optional[SpecOptionRepository] = None
    ):
        """初始化SKU服务

        Args:
            redis: Redis客户端，用于缓存和分布式锁
            sku_repo: SKU仓库实例，不提供则创建新实例
            spec_repo: 规格仓库实例，不提供则创建新实例
            spec_option_repo: 规格选项仓库实例，不提供则创建新实例
        """
        BaseService.__init__(self, redis)
        self.sku_repo = sku_repo
        self.spec_repo = spec_repo
        self.spec_option_repo = spec_option_repo

    # === 缓存相关方法 ===

    async def _cache_sku(self, sku_id: int, sku_data: ProductSKUResponse) -> None:
        """缓存SKU数据"""
        if self.redis:
            cache_key = f"sku:{sku_id}"
            await self.redis.setex(
                cache_key,
                3600,  # 1小时缓存
                sku_data.model_dump_json()
            )

    async def _get_cached_sku(self, sku_id: int) -> Optional[ProductSKUResponse]:
        """从缓存获取SKU数据"""
        if not self.redis:
            return None

        cache_key = f"sku:{sku_id}"
        cached_data = await self.redis.get(cache_key)
        if cached_data:
            return ProductSKUResponse.model_validate_json(cached_data)
        return None

    async def _invalidate_sku_cache(self, sku_id: int) -> None:
        """清除SKU缓存"""
        if self.redis:
            cache_key = f"sku:{sku_id}"
            await self.redis.delete(cache_key)

    async def _invalidate_product_sku_cache(self, product_id: int) -> None:
        """清除产品相关的SKU缓存"""
        if self.redis:
            pattern = f"product:{product_id}:skus:*"
            keys = await self.redis.keys(pattern)
            if keys:
                await self.redis.delete(*keys)

    # === 核心业务方法 ===

    async def create_sku(self, params: ProductSKUCreate) -> Result[ProductSKUResponse]:
        """创建SKU"""
        try:
            self.logger.info(f"创建SKU: product_id={params.product_id}, sku={params.sku}")

            # 检查SKU编码是否已存在
            existing_sku = await self.sku_repo.get_one(sku=params.sku)
            if existing_sku:
                self.logger.warning(f"SKU编码已存在: {params.sku}")
                return self.create_error_result(
                    error_code=ErrorCode.ALREADY_EXISTS,
                    error_message=f"SKU编码 {params.sku} 已被使用"
                )

            # 验证规格选项是否存在
            if params.spec_option_ids:
                for option_id in params.spec_option_ids:
                    option = await self.spec_option_repo.get_by_id(option_id)
                    if not option:
                        return self.create_error_result(
                            error_code=ErrorCode.NOT_FOUND,
                            error_message=f"规格选项 {option_id} 不存在"
                        )

            # 创建SKU
            sku_data = params.model_dump()
            sku = await self.sku_repo.create(sku_data)

            # 关联规格选项
            if params.spec_option_ids:
                await self._associate_spec_options(sku.id, params.spec_option_ids)

            # 构建响应
            sku_response = ProductSKUResponse.model_validate(sku.to_dict())

            # 缓存SKU
            await self._cache_sku(sku.id, sku_response)

            # 清除相关缓存
            await self._invalidate_product_sku_cache(params.product_id)

            # 触发SKU创建事件
            event_data = sku_response.model_dump()
            dispatch("products:sku:created", payload=event_data)

            self.logger.info(f"SKU创建成功: id={sku.id}, sku={sku.sku}")
            return self.create_success_result(sku_response, status_code=201)

        except Exception as e:
            self.logger.error(f"创建SKU失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"创建SKU失败: {str(e)}"
            )

    async def get_sku(self, sku_id: int, user_mode: bool = False) -> Result[ProductSKUResponse]:
        """获取SKU详情"""
        try:
            self.logger.info(f"获取SKU详情: id={sku_id}")

            # 先尝试从缓存获取
            cached_sku = await self._get_cached_sku(sku_id)
            if cached_sku:
                self.logger.debug(f"从缓存获取到SKU: id={sku_id}")
                return self.create_success_result(cached_sku)

            # 从数据库获取
            sku = await self.sku_repo.get_by_id(sku_id)
            if not sku:
                self.logger.warning(f"SKU不存在: id={sku_id}")
                return self.resource_not_found_result(sku_id)

            # 构建响应并缓存
            sku_response = ProductSKUResponse.model_validate(sku.to_dict())
            await self._cache_sku(sku.id, sku_response)

            self.logger.info(f"获取SKU详情成功: id={sku_id}")
            return self.create_success_result(sku_response)

        except Exception as e:
            self.logger.error(f"获取SKU详情失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取SKU详情失败: {str(e)}"
            )

    async def update_sku(self, sku_id: int, params: ProductSKUUpdate) -> Result[ProductSKUResponse]:
        """更新SKU"""
        try:
            self.logger.info(f"更新SKU: id={sku_id}")

            # 检查SKU是否存在
            sku = await self.sku_repo.get_by_id(sku_id)
            if not sku:
                self.logger.warning(f"SKU不存在: id={sku_id}")
                return self.resource_not_found_result(sku_id)

            # 如果更新SKU编码，检查是否重复
            if params.sku and params.sku != sku.sku:
                existing_sku = await self.sku_repo.get_one(sku=params.sku)
                if existing_sku:
                    return self.create_error_result(
                        error_code=ErrorCode.ALREADY_EXISTS,
                        error_message=f"SKU编码 {params.sku} 已被使用"
                    )

            # 验证规格选项是否存在
            if params.spec_option_ids is not None:
                for option_id in params.spec_option_ids:
                    option = await self.spec_option_repo.get_by_id(option_id)
                    if not option:
                        return self.create_error_result(
                            error_code=ErrorCode.NOT_FOUND,
                            error_message=f"规格选项 {option_id} 不存在"
                        )

            # 更新SKU
            update_data = {k: v for k, v in params.model_dump().items() if v is not None}
            updated_sku = await self.sku_repo.update(sku, update_data)

            # 更新规格选项关联
            if params.spec_option_ids is not None:
                await self._update_spec_options(sku_id, params.spec_option_ids)

            # 构建响应
            sku_response = ProductSKUResponse.model_validate(updated_sku.to_dict())

            # 更新缓存
            await self._cache_sku(sku_id, sku_response)

            # 清除相关缓存
            await self._invalidate_product_sku_cache(updated_sku.product_id)

            # 触发SKU更新事件
            event_data = sku_response.model_dump()
            dispatch("products:sku:updated", payload=event_data)

            self.logger.info(f"SKU更新成功: id={sku_id}")
            return self.create_success_result(sku_response)

        except Exception as e:
            self.logger.error(f"更新SKU失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"更新SKU失败: {str(e)}"
            )

    async def delete_sku(self, sku_id: int) -> Result[Dict[str, Any]]:
        """删除SKU（软删除）"""
        try:
            self.logger.info(f"删除SKU: id={sku_id}")

            # 检查SKU是否存在
            sku = await self.sku_repo.get_by_id(sku_id)
            if not sku:
                self.logger.warning(f"SKU不存在: id={sku_id}")
                return self.resource_not_found_result(sku_id)

            # 软删除SKU
            await self.sku_repo.soft_delete(sku)

            # 清除缓存
            await self._invalidate_sku_cache(sku_id)
            await self._invalidate_product_sku_cache(sku.product_id)

            # 触发SKU删除事件
            event_data = {"sku_id": sku_id, "product_id": sku.product_id}
            dispatch("products:sku:deleted", payload=event_data)

            self.logger.info(f"SKU删除成功: id={sku_id}")
            return self.create_success_result({"message": "SKU删除成功"})

        except Exception as e:
            self.logger.error(f"删除SKU失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"删除SKU失败: {str(e)}"
            )

    async def get_product_skus(self, product_id: int, user_mode: bool = False) -> Result[ProductSKUListResponse]:
        """获取产品的所有SKU"""
        try:
            self.logger.info(f"获取产品SKU列表: product_id={product_id}")

            # 从数据库获取
            skus = await self.sku_repo.get_skus_by_product(product_id)

            # 构建响应
            sku_responses = [ProductSKUResponse.model_validate(sku.to_dict()) for sku in skus]

            # 构建分页响应
            response = ProductSKUListResponse(
                items=sku_responses,
                total=len(sku_responses),
                page_num=1,
                page_size=len(sku_responses),
                total_pages=1
            )

            self.logger.info(f"获取产品SKU列表成功: product_id={product_id}, count={len(skus)}")
            return self.create_success_result(response)

        except Exception as e:
            self.logger.error(f"获取产品SKU列表失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取产品SKU列表失败: {str(e)}"
            )

    async def update_sku_stock(self, sku_id: int, quantity_change: int) -> Result[ProductSKUResponse]:
        """更新SKU库存"""
        try:
            self.logger.info(f"更新SKU库存: id={sku_id}, change={quantity_change}")

            # 检查SKU是否存在
            sku = await self.sku_repo.get_by_id(sku_id)
            if not sku:
                self.logger.warning(f"SKU不存在: id={sku_id}")
                return self.resource_not_found_result(sku_id)

            # 计算新库存
            new_quantity = max(0, sku.stock_quantity + quantity_change)

            # 更新库存
            update_data = {"stock_quantity": new_quantity}
            updated_sku = await self.sku_repo.update(sku, update_data)

            # 构建响应
            sku_response = ProductSKUResponse.model_validate(updated_sku.to_dict())

            # 更新缓存
            await self._cache_sku(sku_id, sku_response)

            # 触发库存更新事件
            event_data = {
                "sku_id": sku_id,
                "product_id": updated_sku.product_id,
                "old_quantity": sku.stock_quantity,
                "new_quantity": new_quantity,
                "change": quantity_change
            }
            dispatch("products:sku:stock_updated", payload=event_data)

            self.logger.info(f"SKU库存更新成功: id={sku_id}, new_quantity={new_quantity}")
            return self.create_success_result(sku_response)

        except Exception as e:
            self.logger.error(f"更新SKU库存失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"更新SKU库存失败: {str(e)}"
            )

    # === 辅助方法 ===

    async def _associate_spec_options(self, sku_id: int, option_ids: List[int]) -> None:
        """关联SKU与规格选项"""
        try:
            # 获取SKU对象
            sku = await self.sku_repo.get_by_id(sku_id)
            if not sku:
                return

            # 获取规格选项对象
            options = []
            for option_id in option_ids:
                option = await self.spec_option_repo.get_by_id(option_id)
                if option:
                    options.append(option)

            # 关联规格选项
            sku.spec_options = options
            await self.sku_repo.db.commit()

        except Exception as e:
            self.logger.error(f"关联规格选项失败: sku_id={sku_id}, error={str(e)}")
            raise

    async def _update_spec_options(self, sku_id: int, option_ids: List[int]) -> None:
        """更新SKU的规格选项关联"""
        try:
            # 获取SKU对象
            sku = await self.sku_repo.get_by_id(sku_id)
            if not sku:
                return

            # 清除现有关联
            sku.spec_options.clear()

            # 添加新的关联
            if option_ids:
                options = []
                for option_id in option_ids:
                    option = await self.spec_option_repo.get_by_id(option_id)
                    if option:
                        options.append(option)
                sku.spec_options = options

            await self.sku_repo.db.commit()

        except Exception as e:
            self.logger.error(f"更新规格选项关联失败: sku_id={sku_id}, error={str(e)}")
            raise

    async def get_skus_by_spec_option(self, option_id: int) -> Result[ProductSKUListResponse]:
        """根据规格选项获取SKU列表"""
        try:
            self.logger.info(f"根据规格选项获取SKU列表: option_id={option_id}")

            # 从数据库获取
            skus = await self.sku_repo.get_skus_by_option(option_id)

            # 构建响应
            sku_responses = [ProductSKUResponse.model_validate(sku.to_dict()) for sku in skus]

            # 构建分页响应
            response = ProductSKUListResponse(
                items=sku_responses,
                total=len(sku_responses),
                page_num=1,
                page_size=len(sku_responses),
                total_pages=1
            )

            self.logger.info(f"根据规格选项获取SKU列表成功: option_id={option_id}, count={len(skus)}")
            return self.create_success_result(response)

        except Exception as e:
            self.logger.error(f"根据规格选项获取SKU列表失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"根据规格选项获取SKU列表失败: {str(e)}"
            )

    async def generate_sku_code(self, product_id: int, spec_option_ids: List[int]) -> str:
        """生成SKU编码"""
        try:
            # 获取产品信息
            product = await self.sku_repo.db.get(ProductSKU, product_id)
            if not product:
                raise ValueError(f"产品不存在: {product_id}")

            # 基础SKU编码
            base_sku = f"P{product_id:06d}"

            # 如果有规格选项，添加规格编码
            if spec_option_ids:
                spec_codes = []
                for option_id in sorted(spec_option_ids):
                    option = await self.spec_option_repo.get_by_id(option_id)
                    if option:
                        # 使用规格ID和选项ID生成编码
                        spec_codes.append(f"S{option.spec_id}O{option_id}")

                if spec_codes:
                    base_sku += "-" + "-".join(spec_codes)

            # 确保SKU编码唯一
            counter = 1
            sku_code = base_sku
            while await self.sku_repo.get_one(sku=sku_code):
                sku_code = f"{base_sku}-{counter:03d}"
                counter += 1

            return sku_code

        except Exception as e:
            self.logger.error(f"生成SKU编码失败: error={str(e)}")
            # 返回一个基础的时间戳编码作为后备
            import time
            return f"SKU{int(time.time())}"

    async def batch_create_skus(self, skus_data: List[ProductSKUCreate]) -> Result[List[ProductSKUResponse]]:
        """批量创建SKU"""
        try:
            self.logger.info(f"批量创建SKU: count={len(skus_data)}")

            created_skus = []
            errors = []

            for i, sku_data in enumerate(skus_data):
                try:
                    result = await self.create_sku(sku_data)
                    if result.success:
                        created_skus.append(result.data)
                    else:
                        errors.append(f"第{i+1}个SKU创建失败: {result.message}")
                except Exception as e:
                    errors.append(f"第{i+1}个SKU创建异常: {str(e)}")

            if errors:
                self.logger.warning(f"批量创建SKU部分失败: {errors}")
                return self.create_error_result(
                    error_code=ErrorCode.PARTIAL_SUCCESS,
                    error_message=f"部分SKU创建失败: {'; '.join(errors)}",
                    data={"created": created_skus, "errors": errors}
                )

            self.logger.info(f"批量创建SKU成功: count={len(created_skus)}")
            return self.create_success_result(created_skus, status_code=201)

        except Exception as e:
            self.logger.error(f"批量创建SKU失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"批量创建SKU失败: {str(e)}"
            )