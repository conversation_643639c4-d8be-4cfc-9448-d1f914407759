"""
商品规格API路由
包含规格和规格选项的创建、查询、更新和删除功能
"""
from typing import Any, Optional

from fastapi import APIRouter, Depends, Path, Query, status

from svc.apps.auth.dependencies import (get_current_active_user,
                                        has_permission, resource_permission)
from svc.apps.auth.models.user import User
from svc.apps.products.dependencies import get_spec_repository, get_spec_option_repository
from svc.apps.products.repositories.spec import SpecRepository, SpecOptionRepository
from svc.apps.products.schemas.spec import (
    SpecCreate,
    SpecUpdate,
    SpecResponse,
    SpecListResponse,
    SpecOptionCreate,
    SpecOptionUpdate,
    SpecOptionResponse,
    SpecOptionListResponse
)
from svc.core.exceptions.route_error_handler import handle_route_errors
from svc.core.schemas.base import PageParams
from svc.core.models.result import Result

# 创建路由器
router = APIRouter(
    tags=["商品规格管理"]
)

# 错误映射（可以根据需要扩展）
SPEC_ERROR_MAPPING = {}

# === 管理端路由 (Admin Routes) ===

@router.get("/admin/specs", response_model=Result[SpecListResponse])
@handle_route_errors(SPEC_ERROR_MAPPING)
async def admin_list_specs(
    params: PageParams = Depends(),
    status: Optional[str] = Query(None, description="规格状态"),
    search_term: Optional[str] = Query(None, description="搜索关键词", alias="searchTerm"),
    spec_repo: SpecRepository = Depends(get_spec_repository),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("spec:read"))
) -> Result[SpecListResponse]:
    """获取规格列表 (管理端)"""
    try:
        # 构建查询条件
        filters = {}
        if status:
            filters["status"] = status
        if search_term:
            filters["name__icontains"] = search_term
        
        # 获取规格列表
        specs, total = await spec_repo.get_list(
            skip=(params.page_num - 1) * params.page_size,
            limit=params.page_size,
            **filters
        )
        
        # 构建响应
        spec_responses = [SpecResponse.model_validate(spec.to_dict()) for spec in specs]
        response = SpecListResponse(
            items=spec_responses,
            total=total,
            page_num=params.page_num,
            page_size=params.page_size,
            total_pages=(total + params.page_size - 1) // params.page_size
        )
        
        return Result.success(response)
    except Exception as e:
        return Result.error(f"获取规格列表失败: {str(e)}")

@router.get("/admin/specs/{spec_id}", response_model=Result[SpecResponse])
@handle_route_errors(SPEC_ERROR_MAPPING)
async def admin_get_spec(
    spec_id: int = Path(..., description="规格ID"),
    spec_repo: SpecRepository = Depends(get_spec_repository),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("spec:read"))
) -> Result[SpecResponse]:
    """获取规格详情 (管理端)"""
    try:
        spec = await spec_repo.get_by_id(spec_id)
        if not spec:
            return Result.error("规格不存在", status_code=404)
        
        response = SpecResponse.model_validate(spec.to_dict())
        return Result.success(response)
    except Exception as e:
        return Result.error(f"获取规格详情失败: {str(e)}")

@router.post("/admin/specs", response_model=Result[SpecResponse], status_code=status.HTTP_201_CREATED)
@handle_route_errors(SPEC_ERROR_MAPPING)
async def admin_create_spec(
    spec_data: SpecCreate,
    spec_repo: SpecRepository = Depends(get_spec_repository),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("spec:create"))
) -> Result[SpecResponse]:
    """创建规格 (管理端)"""
    try:
        # 检查规格名称是否已存在
        existing_spec = await spec_repo.get_one(name=spec_data.name)
        if existing_spec:
            return Result.error(f"规格名称 '{spec_data.name}' 已存在", status_code=400)
        
        # 创建规格
        spec = await spec_repo.create(spec_data.model_dump())
        response = SpecResponse.model_validate(spec.to_dict())
        
        return Result.success(response, status_code=201)
    except Exception as e:
        return Result.error(f"创建规格失败: {str(e)}")

@router.put("/admin/specs/{spec_id}", response_model=Result[SpecResponse])
@handle_route_errors(SPEC_ERROR_MAPPING)
async def admin_update_spec(
    spec_data: SpecUpdate,
    spec_id: int = Path(..., description="规格ID"),
    spec_repo: SpecRepository = Depends(get_spec_repository),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("spec", "update"))
) -> Result[SpecResponse]:
    """更新规格 (管理端)"""
    try:
        spec = await spec_repo.get_by_id(spec_id)
        if not spec:
            return Result.error("规格不存在", status_code=404)
        
        # 如果更新名称，检查是否重复
        if spec_data.name and spec_data.name != spec.name:
            existing_spec = await spec_repo.get_one(name=spec_data.name)
            if existing_spec:
                return Result.error(f"规格名称 '{spec_data.name}' 已存在", status_code=400)
        
        # 更新规格
        update_data = {k: v for k, v in spec_data.model_dump().items() if v is not None}
        updated_spec = await spec_repo.update(spec, update_data)
        response = SpecResponse.model_validate(updated_spec.to_dict())
        
        return Result.success(response)
    except Exception as e:
        return Result.error(f"更新规格失败: {str(e)}")

@router.delete("/admin/specs/{spec_id}", response_model=Result)
@handle_route_errors(SPEC_ERROR_MAPPING)
async def admin_delete_spec(
    spec_id: int = Path(..., description="规格ID"),
    spec_repo: SpecRepository = Depends(get_spec_repository),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("spec", "delete"))
) -> Result:
    """删除规格 (管理端)"""
    try:
        spec = await spec_repo.get_by_id(spec_id)
        if not spec:
            return Result.error("规格不存在", status_code=404)
        
        # 软删除规格
        await spec_repo.soft_delete(spec)
        return Result.success({"message": "规格删除成功"})
    except Exception as e:
        return Result.error(f"删除规格失败: {str(e)}")

# === 规格选项管理端路由 ===

@router.get("/admin/specs/{spec_id}/options", response_model=Result[SpecOptionListResponse])
@handle_route_errors(SPEC_ERROR_MAPPING)
async def admin_list_spec_options(
    spec_id: int = Path(..., description="规格ID"),
    params: PageParams = Depends(),
    spec_option_repo: SpecOptionRepository = Depends(get_spec_option_repository),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("spec:read"))
) -> Result[SpecOptionListResponse]:
    """获取规格选项列表 (管理端)"""
    try:
        # 获取规格选项列表
        options = await spec_option_repo.get_options_by_spec(spec_id)
        
        # 构建响应
        option_responses = [SpecOptionResponse.model_validate(option.to_dict()) for option in options]
        response = SpecOptionListResponse(
            items=option_responses,
            total=len(option_responses),
            page_num=1,
            page_size=len(option_responses),
            total_pages=1
        )
        
        return Result.success(response)
    except Exception as e:
        return Result.error(f"获取规格选项列表失败: {str(e)}")

@router.post("/admin/specs/{spec_id}/options", response_model=Result[SpecOptionResponse], status_code=status.HTTP_201_CREATED)
@handle_route_errors(SPEC_ERROR_MAPPING)
async def admin_create_spec_option(
    option_data: SpecOptionCreate,
    spec_id: int = Path(..., description="规格ID"),
    spec_option_repo: SpecOptionRepository = Depends(get_spec_option_repository),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("spec:create"))
) -> Result[SpecOptionResponse]:
    """创建规格选项 (管理端)"""
    try:
        # 设置规格ID
        option_data.spec_id = spec_id
        
        # 检查选项值是否已存在
        existing_option = await spec_option_repo.get_one(spec_id=spec_id, value=option_data.value)
        if existing_option:
            return Result.error(f"规格选项值 '{option_data.value}' 已存在", status_code=400)
        
        # 创建规格选项
        option = await spec_option_repo.create(option_data.model_dump())
        response = SpecOptionResponse.model_validate(option.to_dict())
        
        return Result.success(response, status_code=201)
    except Exception as e:
        return Result.error(f"创建规格选项失败: {str(e)}")

# === 客户端路由 (Client Routes) ===

@router.get("/specs", response_model=Result[SpecListResponse])
@handle_route_errors(SPEC_ERROR_MAPPING)
async def list_specs(
    params: PageParams = Depends(),
    category_id: Optional[int] = Query(None, description="分类ID", alias="categoryId"),
    spec_repo: SpecRepository = Depends(get_spec_repository)
) -> Result[SpecListResponse]:
    """获取规格列表 (客户端)"""
    try:
        # 构建查询条件
        filters = {"status": "active"}
        if category_id:
            # 这里需要根据分类获取规格，需要实现相应的仓库方法
            specs = await spec_repo.get_specs_by_category(category_id)
            total = len(specs)
        else:
            specs, total = await spec_repo.get_list(
                skip=(params.page_num - 1) * params.page_size,
                limit=params.page_size,
                **filters
            )
        
        # 构建响应
        spec_responses = [SpecResponse.model_validate(spec.to_dict()) for spec in specs]
        response = SpecListResponse(
            items=spec_responses,
            total=total,
            page_num=params.page_num,
            page_size=params.page_size,
            total_pages=(total + params.page_size - 1) // params.page_size
        )
        
        return Result.success(response)
    except Exception as e:
        return Result.error(f"获取规格列表失败: {str(e)}")

@router.get("/specs/{spec_id}/options", response_model=Result[SpecOptionListResponse])
@handle_route_errors(SPEC_ERROR_MAPPING)
async def list_spec_options(
    spec_id: int = Path(..., description="规格ID"),
    spec_option_repo: SpecOptionRepository = Depends(get_spec_option_repository)
) -> Result[SpecOptionListResponse]:
    """获取规格选项列表 (客户端)"""
    try:
        # 获取活跃的规格选项
        options = await spec_option_repo.get_options_by_spec(spec_id)
        
        # 构建响应
        option_responses = [SpecOptionResponse.model_validate(option.to_dict()) for option in options]
        response = SpecOptionListResponse(
            items=option_responses,
            total=len(option_responses),
            page_num=1,
            page_size=len(option_responses),
            total_pages=1
        )
        
        return Result.success(response)
    except Exception as e:
        return Result.error(f"获取规格选项列表失败: {str(e)}")
