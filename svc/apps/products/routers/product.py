"""
商品API路由
包含商品的创建、查询、更新和删除功能
"""
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, Path, Query, status

from svc.apps.auth.dependencies import (get_current_active_user,
                                        has_permission, resource_permission)
from svc.apps.auth.models.user import User
from svc.apps.products.dependencies import get_product_service
from svc.apps.products.schemas.product import (GetProductsParams,
                                               ProductBatchUpdateRequest,
                                               ProductCreate,
                                               ProductListResponse,
                                               ProductUpdate,
                                               UserProductListResponse,
                                               UserProductResponse)
from svc.apps.products.services.product import ProductService
from svc.core.exceptions.route_error_handler import (PRODUCT_ERROR_MAPPING,
                                                     handle_route_errors)
from svc.core.models.result import Result
from svc.core.schemas.base import PageParams
from svc.core.schemas.batch import BatchUpdateResponse

# 创建路由器
router = APIRouter(
    tags=["商品管理"]
)

# === 管理端路由 (Admin Routes) ===

@router.get("/admin/list", response_model=Result[ProductListResponse])
@handle_route_errors(PRODUCT_ERROR_MAPPING)
async def admin_list_products(
    params: PageParams = Depends(),
    status: Optional[str] = Query(None, description="商品状态", alias="status"),
    category_id: Optional[int] = Query(None, description="分类ID", alias="categoryId"),
    is_featured: Optional[bool] = Query(None, description="是否推荐商品", alias="isFeatured"),
    search_term: Optional[str] = Query(None, description="搜索关键词", alias="searchTerm"),
    min_price: Optional[float] = Query(None, description="最低价格", alias="minPrice"),
    max_price: Optional[float] = Query(None, description="最高价格", alias="maxPrice"),
    order_by: Optional[str] = Query("created_at", description="排序字段", alias="orderBy"),
    order_desc: Optional[bool] = Query(True, description="是否降序", alias="orderDesc"),
    product_service: ProductService = Depends(get_product_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("product:read"))
) -> Result[ProductListResponse]:
    """获取商品列表 (管理端)"""
    params_obj = GetProductsParams(
        page_num=params.page_num,
        page_size=params.page_size,
        status=status,
        category_id=category_id,
        is_featured=is_featured,
        search_term=search_term,
        min_price=min_price,
        max_price=max_price,
        order_by=order_by,
        order_desc=order_desc
    )
    result = await product_service.get_products(params=params_obj)
    return result


@router.post("/admin/create", response_model=Result, status_code=status.HTTP_201_CREATED)
@handle_route_errors(PRODUCT_ERROR_MAPPING)
async def admin_create_product(
    product_data: ProductCreate,
    product_service: ProductService = Depends(get_product_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("product:create")),
) -> Result:
    """创建商品 (管理端)"""
    result = await product_service.create_product(params=product_data)
    return result

@router.put("/admin/batch", response_model=Result[BatchUpdateResponse])
@handle_route_errors(PRODUCT_ERROR_MAPPING)
async def admin_batch_update_products(
    request: ProductBatchUpdateRequest,
    product_service: ProductService = Depends(get_product_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(has_permission("product:batch_update"))
) -> Result[BatchUpdateResponse]:
    """批量更新商品 (管理端)"""
    result = await product_service.batch_update_products(request)
    return result

@router.get("/admin/{product_id}", response_model=Result)
@handle_route_errors(PRODUCT_ERROR_MAPPING)
async def admin_get_product(
    product_id: int = Path(..., description="商品ID"),
    product_service: ProductService = Depends(get_product_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("product:read"))
) -> Result:
    """获取商品详情 (管理端)"""
    result = await product_service.get_product(product_id=product_id, user_mode=False)
    return result

@router.put("/admin/{product_id}", response_model=Result)
@handle_route_errors(PRODUCT_ERROR_MAPPING)
async def admin_update_product(
    product_in: ProductUpdate,
    product_id: int = Path(..., description="商品ID"),
    product_service: ProductService = Depends(get_product_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("product", "update")),
) -> Result:
    """更新商品 (管理端)"""
    result = await product_service.update_product(product_id=product_id, params=product_in)
    return result

@router.delete("/admin/{product_id}", response_model=Result)
@handle_route_errors(PRODUCT_ERROR_MAPPING)
async def admin_delete_product(
    product_id: int = Path(..., description="商品ID"),
    product_service: ProductService = Depends(get_product_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("product", "delete")),
) -> Result:
    """删除商品 (管理端)"""
    # 这里应该实现软删除逻辑
    result = await product_service.delete_product(product_id=product_id)
    return result


# === 客户端路由 (Client Routes) ===

@router.get("/list", response_model=Result[UserProductListResponse])
@handle_route_errors(PRODUCT_ERROR_MAPPING)
async def list_products(
    params: PageParams = Depends(),
    is_featured: Optional[bool] = Query(True, description="是否推荐商品", alias="isFeatured"),
    category_id: Optional[int] = Query(None, description="分类ID", alias="categoryId"),
    search_term: Optional[str] = Query(None, description="搜索关键词", alias="searchTerm"),
    min_price: Optional[float] = Query(None, description="最低价格", alias="minPrice"),
    max_price: Optional[float] = Query(None, description="最高价格", alias="maxPrice"),
    order_by: Optional[str] = Query("created_at", description="排序字段", alias="orderBy"),
    order_desc: Optional[bool] = Query(True, description="是否降序", alias="orderDesc"),
    product_service: ProductService = Depends(get_product_service),
) -> Result[UserProductListResponse]:
    """获取商品列表 (客户端)"""
    params_obj = GetProductsParams(
        page_num=params.page_num,
        page_size=params.page_size,
        status="active",  # 客户端只显示上架商品
        category_id=category_id,
        search_term=search_term,
        min_price=min_price,
        max_price=max_price,
        order_by=order_by,
        order_desc=order_desc,
        is_featured=is_featured
    )
    result = await product_service.get_products(params=params_obj, user_mode=True)
    return result



@router.get("/{product_id}", response_model=Result[UserProductResponse])
@handle_route_errors(PRODUCT_ERROR_MAPPING)
async def get_product_details(
    product_id: int = Path(..., description="商品ID"),
    product_service: ProductService = Depends(get_product_service),
) -> Result[UserProductResponse]:
    """获取商品详情 (客户端)"""
    result = await product_service.get_product(product_id=product_id, user_mode=True)
    return result

