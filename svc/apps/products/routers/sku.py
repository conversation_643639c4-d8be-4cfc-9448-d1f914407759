"""
商品SKU API路由
包含SKU的创建、查询、更新和删除功能
"""
from typing import Any, List, Optional

from fastapi import APIRouter, Depends, Path, Query, status

from svc.apps.auth.dependencies import (get_current_active_user,
                                        has_permission, resource_permission)
from svc.apps.auth.models.user import User
from svc.apps.products.dependencies import get_sku_service
from svc.apps.products.schemas.product import (ProductSKUCreate,
                                               ProductSKUListResponse,
                                               ProductSKUResponse,
                                               ProductSKUUpdate)
from svc.apps.products.services.sku import ProductSKUService
from svc.core.exceptions.route_error_handler import (SKU_ERROR_MAPPING,
                                                     handle_route_errors)
from svc.core.models.result import Result
from svc.core.schemas.base import PageParams

# 创建路由器
router = APIRouter(
    tags=["商品SKU管理"]
)


# === 管理端路由 (Admin Routes) ===

@router.get("/admin/skus", response_model=Result[ProductSKUListResponse])
@handle_route_errors(SKU_ERROR_MAPPING)
async def admin_list_skus(
    params: PageParams = Depends(),
    product_id: Optional[int] = Query(None, description="产品ID", alias="productId"),
    status: Optional[str] = Query(None, description="SKU状态"),
    sku_service: ProductSKUService = Depends(get_sku_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("sku:read"))
) -> Result[ProductSKUListResponse]:
    """获取SKU列表 (管理端)"""
    if product_id:
        # 获取指定产品的SKU列表
        result = await sku_service.get_product_skus(product_id=product_id, user_mode=False)
    else:
        # TODO: 实现全局SKU列表查询
        # 目前先返回空列表
        response = ProductSKUListResponse(
            items=[],
            total=0,
            page_num=params.page_num,
            page_size=params.page_size,
            total_pages=0
        )
        result = Result.success(response)
    
    return result

@router.get("/admin/skus/{sku_id}", response_model=Result[ProductSKUResponse])
@handle_route_errors(SKU_ERROR_MAPPING)
async def admin_get_sku(
    sku_id: int = Path(..., description="SKU ID"),
    sku_service: ProductSKUService = Depends(get_sku_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("sku:read"))
) -> Result[ProductSKUResponse]:
    """获取SKU详情 (管理端)"""
    result = await sku_service.get_sku(sku_id=sku_id, user_mode=False)
    return result

@router.post("/admin/skus", response_model=Result[ProductSKUResponse], status_code=status.HTTP_201_CREATED)
@handle_route_errors(SKU_ERROR_MAPPING)
async def admin_create_sku(
    sku_data: ProductSKUCreate,
    sku_service: ProductSKUService = Depends(get_sku_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("sku:create"))
) -> Result[ProductSKUResponse]:
    """创建SKU (管理端)"""
    result = await sku_service.create_sku(params=sku_data)
    return result

@router.put("/admin/skus/{sku_id}", response_model=Result[ProductSKUResponse])
@handle_route_errors(SKU_ERROR_MAPPING)
async def admin_update_sku(
    sku_data: ProductSKUUpdate,
    sku_id: int = Path(..., description="SKU ID"),
    sku_service: ProductSKUService = Depends(get_sku_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("sku", "update"))
) -> Result[ProductSKUResponse]:
    """更新SKU (管理端)"""
    result = await sku_service.update_sku(sku_id=sku_id, params=sku_data)
    return result

@router.delete("/admin/skus/{sku_id}", response_model=Result)
@handle_route_errors(SKU_ERROR_MAPPING)
async def admin_delete_sku(
    sku_id: int = Path(..., description="SKU ID"),
    sku_service: ProductSKUService = Depends(get_sku_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("sku", "delete"))
) -> Result:
    """删除SKU (管理端)"""
    result = await sku_service.delete_sku(sku_id=sku_id)
    return result

@router.post("/admin/skus/batch", response_model=Result[List[ProductSKUResponse]], status_code=status.HTTP_201_CREATED)
@handle_route_errors(SKU_ERROR_MAPPING)
async def admin_batch_create_skus(
    skus_data: List[ProductSKUCreate],
    sku_service: ProductSKUService = Depends(get_sku_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("sku:create"))
) -> Result[List[ProductSKUResponse]]:
    """批量创建SKU (管理端)"""
    result = await sku_service.batch_create_skus(skus_data=skus_data)
    return result

@router.put("/admin/skus/{sku_id}/stock", response_model=Result[ProductSKUResponse])
@handle_route_errors(SKU_ERROR_MAPPING)
async def admin_update_sku_stock(
    quantity_change: int = Query(..., description="库存变化量（正数增加，负数减少）", alias="quantityChange"),
    sku_id: int = Path(..., description="SKU ID"),
    sku_service: ProductSKUService = Depends(get_sku_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("sku", "update"))
) -> Result[ProductSKUResponse]:
    """更新SKU库存 (管理端)"""
    result = await sku_service.update_sku_stock(sku_id=sku_id, quantity_change=quantity_change)
    return result

# === 产品相关的SKU路由 ===

@router.get("/admin/products/{product_id}/skus", response_model=Result[ProductSKUListResponse])
@handle_route_errors(SKU_ERROR_MAPPING)
async def admin_list_product_skus(
    product_id: int = Path(..., description="产品ID"),
    sku_service: ProductSKUService = Depends(get_sku_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("sku:read"))
) -> Result[ProductSKUListResponse]:
    """获取产品的所有SKU (管理端)"""
    result = await sku_service.get_product_skus(product_id=product_id, user_mode=False)
    return result

@router.post("/admin/products/{product_id}/skus/generate", response_model=Result[List[ProductSKUResponse]], status_code=status.HTTP_201_CREATED)
@handle_route_errors(SKU_ERROR_MAPPING)
async def admin_generate_product_skus(
    product_id: int = Path(..., description="产品ID"),
    sku_service: ProductSKUService = Depends(get_sku_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("sku:create"))
) -> Result[List[ProductSKUResponse]]:
    """为产品生成所有可能的SKU组合 (管理端)"""
    # TODO: 实现基于产品规格自动生成SKU的功能
    # 这需要与规格服务集成，根据产品的规格选项生成所有可能的组合
    try:
        return Result.error("SKU自动生成功能尚未实现", status_code=501)
    except Exception as e:
        return Result.error(f"生成SKU失败: {str(e)}")

# === 规格选项相关的SKU路由 ===

@router.get("/admin/spec-options/{option_id}/skus", response_model=Result[ProductSKUListResponse])
@handle_route_errors(SKU_ERROR_MAPPING)
async def admin_list_skus_by_spec_option(
    option_id: int = Path(..., description="规格选项ID"),
    sku_service: ProductSKUService = Depends(get_sku_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("sku:read"))
) -> Result[ProductSKUListResponse]:
    """根据规格选项获取SKU列表 (管理端)"""
    result = await sku_service.get_skus_by_spec_option(option_id=option_id)
    return result

# === 客户端路由 (Client Routes) ===

@router.get("/products/{product_id}/skus", response_model=Result[ProductSKUListResponse])
@handle_route_errors(SKU_ERROR_MAPPING)
async def list_product_skus(
    product_id: int = Path(..., description="产品ID"),
    sku_service: ProductSKUService = Depends(get_sku_service)
) -> Result[ProductSKUListResponse]:
    """获取产品的所有可用SKU (客户端)"""
    result = await sku_service.get_product_skus(product_id=product_id, user_mode=True)
    return result

@router.get("/skus/{sku_id}", response_model=Result[ProductSKUResponse])
@handle_route_errors(SKU_ERROR_MAPPING)
async def get_sku_details(
    sku_id: int = Path(..., description="SKU ID"),
    sku_service: ProductSKUService = Depends(get_sku_service)
) -> Result[ProductSKUResponse]:
    """获取SKU详情 (客户端)"""
    result = await sku_service.get_sku(sku_id=sku_id, user_mode=True)
    return result

# === SKU代码生成工具路由 ===

@router.post("/admin/skus/generate-code", response_model=Result[dict])
@handle_route_errors(SKU_ERROR_MAPPING)
async def admin_generate_sku_code(
    product_id: int = Query(..., description="产品ID", alias="productId"),
    spec_option_ids: List[int] = Query([], description="规格选项ID列表", alias="specOptionIds"),
    sku_service: ProductSKUService = Depends(get_sku_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("sku:create"))
) -> Result[dict]:
    """生成SKU编码 (管理端)"""
    try:
        sku_code = await sku_service.generate_sku_code(
            product_id=product_id,
            spec_option_ids=spec_option_ids
        )
        return Result.success({"sku_code": sku_code})
    except Exception as e:
        return Result.error(f"生成SKU编码失败: {str(e)}")
