from typing import Any, Dict, List

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from svc.apps.products.models.sku import ProductSKU
from svc.apps.products.models.spec import SpecOption
from svc.core.repositories.base import BaseRepository


class ProductSKURepository(BaseRepository[ProductSKU, None, None]):
    """
    SKU仓库，复用BaseRepository，暴露适合服务层调用的接口
    """
    def __init__(self, db: AsyncSession):
        super().__init__(db, ProductSKU)

    async def create(self, data: Dict[str, Any]) -> ProductSKU:
        """
        创建SKU，支持规格选项关联

        Args:
            data: SKU数据字典，包含spec_option_ids

        Returns:
            ProductSKU: 创建的SKU对象
        """
        # 提取规格选项ID列表
        spec_option_ids = data.pop('spec_option_ids', [])

        # 创建SKU基础数据
        sku = await super().create(data)

        # 如果有规格选项ID，建立多对多关联
        if spec_option_ids:
            # 查询规格选项对象
            stmt = select(SpecOption).where(SpecOption.id.in_(spec_option_ids))
            result = await self.db.execute(stmt)
            spec_options = result.scalars().all()

            # 建立多对多关联
            for spec_option in spec_options:
                sku.spec_options.append(spec_option)

            await self.db.flush()
            await self.db.refresh(sku)

        return sku

    async def get_skus_by_product(self, product_id: int) -> List[ProductSKU]:
        """按产品ID查找所有SKU"""
        stmt = select(ProductSKU).where(
            ProductSKU.product_id == product_id,
            ProductSKU.deleted_at.is_(None),
            ProductSKU.status == "active"
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_skus_by_option(self, option_id: int) -> List[ProductSKU]:
        """查找某规格选项下所有SKU"""
        stmt = select(ProductSKU).join(ProductSKU.spec_options).where(
            ProductSKU.deleted_at.is_(None),
            ProductSKU.status == "active",
            ProductSKU.spec_options.any(id=option_id)
        )
        result = await self.db.execute(stmt)
        return result.scalars().all() 