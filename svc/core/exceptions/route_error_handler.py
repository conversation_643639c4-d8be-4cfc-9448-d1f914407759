"""
路由错误处理模块
提供路由装饰器和依赖项，用于统一处理服务返回的Result对象中的错误
"""
import functools
from typing import Any, Callable, Dict, Generic, Optional, TypeVar, Union, cast

from fastapi import Depends, HTTPException, status
from fastapi.responses import JSONResponse

from svc.core.exceptions.error_codes import ErrorCode
from svc.core.models.result import Result, ResultFactory

# 定义泛型类型变量
T = TypeVar('T')

def create_error_response(
    result_code: str, 
    result_msg: Optional[str] = None,
    data: Any = None
) -> Result:
    """创建标准的错误响应
    
    Args:
        result_code: 错误码
        result_msg: 错误消息，如果为None则使用ErrorCode中定义的默认消息
        data: 附加数据
        
    Returns:
        Result: 错误结果对象
    """
    # 如果未提供错误消息，尝试从ErrorCode获取
    if result_msg is None and hasattr(ErrorCode, 'get_msg'):
        try:
            result_msg = ErrorCode.get_msg(result_code)
        except (ValueError, AttributeError):
            result_msg = f"未知错误: {result_code}"
    
    return ResultFactory.error(
        result_code=result_code,
        result_msg=result_msg or f"系统错误: {result_code}",
        data=data
    )

def handle_route_errors(error_mapping: Optional[Dict[str, int]] = None, default_status_code: int = status.HTTP_400_BAD_REQUEST):
    """路由错误处理装饰器，自动处理Result对象中的错误
    
    Args:
        error_mapping: 错误码到HTTP状态码的映射字典
        default_status_code: 默认HTTP状态码
        
    Returns:
        装饰后的函数
    
    Example:
        @router.post("/users", response_model=UserResponse)
        @handle_route_errors(USER_ERROR_MAPPING)
        async def create_user(user_data: UserCreate, service: UserService = Depends(get_user_service)):
            return await service.create_user(user_data)
    """
    def decorator(route_func: Callable[..., Any]) -> Callable[..., Any]:
        @functools.wraps(route_func)
        async def wrapper(*args, **kwargs) -> Any:
            result = await route_func(*args, **kwargs)
            
            # 如果返回的是Result对象，处理其中的错误
            if isinstance(result, Result) and not result.is_success:
                status_code = default_status_code
                
                # 使用错误码映射确定HTTP状态码
                if error_mapping and result.result_code in error_mapping:
                    status_code = error_mapping[result.result_code]
                
                # 添加WWW-Authenticate头（用于认证错误）
                headers = None
                if status_code == status.HTTP_401_UNAUTHORIZED:
                    headers = {"WWW-Authenticate": "Bearer"}
                
                # 使用create_error_response确保错误响应格式一致
                error_result = create_error_response(
                    result_code=result.result_code, 
                    result_msg=result.result_msg,
                    data=result.data
                )
                
                raise HTTPException(
                    status_code=status_code,
                    detail={
                        "code": error_result.result_code,
                        "message": error_result.result_msg,
                        "data": error_result.data
                    },
                    headers=headers
                )
                
            return result
            
        return wrapper
    return decorator

class ResultHandler(Generic[T]):
    """处理服务结果的依赖类
    
    在控制器中可以作为依赖项使用，自动处理Result对象中的错误
    
    Example:
        @router.get("/items/{item_id}", response_model=ItemResponse)
        async def get_item(
            item_id: int, 
            service: ItemService = Depends(get_item_service),
            result_handler: Callable = Depends(ResultHandler(ITEM_ERROR_MAPPING))
        ):
            result = await service.get_item(item_id)
            return result_handler(result)
    """
    
    def __init__(
        self, 
        error_mapping: Optional[Dict[str, int]] = None, 
        default_status_code: int = status.HTTP_400_BAD_REQUEST
    ):
        """初始化结果处理器
        
        Args:
            error_mapping: 错误码到HTTP状态码的映射字典
            default_status_code: 默认HTTP状态码
        """
        self.error_mapping = error_mapping
        self.default_status_code = default_status_code
    
    def __call__(self, result: Result[T]) -> T:
        """处理结果对象
        
        Args:
            result: 服务返回的结果对象
            
        Returns:
            结果数据（如果成功）
            
        Raises:
            HTTPException: 当结果包含错误时抛出
        """
        if not result.is_success:
            status_code = self.default_status_code

            # 使用错误码映射确定HTTP状态码
            if self.error_mapping and result.result_code is not None:
                # 尝试直接匹配
                if result.result_code in self.error_mapping:
                    status_code = self.error_mapping[result.result_code]
                else:
                    # 尝试将整数错误码转换为ErrorCode枚举字符串进行匹配
                    from svc.core.exceptions.error_codes import ErrorCode
                    try:
                        # 如果result_code是整数，尝试找到对应的ErrorCode枚举
                        if isinstance(result.result_code, int):
                            for error_code in ErrorCode:
                                # ErrorCode的value可能是字符串，需要转换比较
                                error_value = error_code.value
                                if isinstance(error_value, str):
                                    try:
                                        error_value = int(error_value)
                                    except (ValueError, TypeError):
                                        continue

                                if error_value == result.result_code:
                                    error_code_str = str(error_code)
                                    if error_code_str in self.error_mapping:
                                        status_code = self.error_mapping[error_code_str]
                                    break
                    except Exception:
                        # 如果转换失败，使用默认状态码
                        pass
            
            # 使用ResultFactory确保错误响应格式一致
            error_result = create_error_response(
                result_code=result.result_code, 
                result_msg=result.result_msg,
                data=result.data
            )
            
            # 添加WWW-Authenticate头（用于认证错误）
            headers = None
            if status_code == status.HTTP_401_UNAUTHORIZED:
                headers = {"WWW-Authenticate": "Bearer"}
            
            raise HTTPException(
                status_code=status_code,
                detail={
                    "code": error_result.result_code,
                    "message": error_result.result_msg,
                    "data": error_result.data
                },
                headers=headers
            )
            
        # 返回结果数据
        return cast(T, result.data)

# 常用错误映射
AUTH_ERROR_MAPPING = {
    str(ErrorCode.UNAUTHORIZED): status.HTTP_401_UNAUTHORIZED,
    str(ErrorCode.TOKEN_EXPIRED): status.HTTP_401_UNAUTHORIZED,
    str(ErrorCode.TOKEN_INVALID): status.HTTP_401_UNAUTHORIZED,
    str(ErrorCode.USER_INACTIVE): status.HTTP_403_FORBIDDEN,
    str(ErrorCode.LOGIN_FAILED): status.HTTP_401_UNAUTHORIZED,
    # str(ErrorCode.INVALID_CREDENTIALS): status.HTTP_401_UNAUTHORIZED, 
}

USER_ERROR_MAPPING = {
    str(ErrorCode.USER_NOT_FOUND): status.HTTP_404_NOT_FOUND,
    str(ErrorCode.USER_EXISTS): status.HTTP_409_CONFLICT,
    str(ErrorCode.PERMISSION_DENIED): status.HTTP_403_FORBIDDEN,
}

ROLE_ERROR_MAPPING = {
    str(ErrorCode.ROLE_NOT_FOUND): status.HTTP_404_NOT_FOUND,
    str(ErrorCode.ROLE_EXISTS): status.HTTP_409_CONFLICT,
    str(ErrorCode.ROLE_IN_USE): status.HTTP_400_BAD_REQUEST,
}

SUBSCRIPTION_ERROR_MAPPING = {
    str(ErrorCode.SUBSCRIPTION_NOT_FOUND): status.HTTP_404_NOT_FOUND,
    str(ErrorCode.SUBSCRIPTION_EXISTS): status.HTTP_409_CONFLICT,
    str(ErrorCode.SUBSCRIPTION_EXPIRED): status.HTTP_400_BAD_REQUEST,
    str(ErrorCode.SUBSCRIPTION_CANCELLED): status.HTTP_400_BAD_REQUEST,
    str(ErrorCode.SUBSCRIPTION_PAUSED): status.HTTP_400_BAD_REQUEST,
    str(ErrorCode.SUBSCRIPTION_RENEWAL_FAILED): status.HTTP_400_BAD_REQUEST,
    str(ErrorCode.SUBSCRIPTION_PAYMENT_REQUIRED): status.HTTP_402_PAYMENT_REQUIRED,
}

SUBSCRIPTION_PLAN_ERROR_MAPPING = {
    str(ErrorCode.SUBSCRIPTION_PLAN_NOT_FOUND): status.HTTP_404_NOT_FOUND,
    str(ErrorCode.SUBSCRIPTION_PLAN_EXISTS): status.HTTP_409_CONFLICT,
    str(ErrorCode.SUBSCRIPTION_PLAN_IN_USE): status.HTTP_400_BAD_REQUEST,
}

INVOICE_ERROR_MAPPING = {
    str(ErrorCode.INVOICE_NOT_FOUND): status.HTTP_404_NOT_FOUND,
    str(ErrorCode.INVOICE_PAID): status.HTTP_409_CONFLICT,
}

PAYMENT_ERROR_MAPPING = {
    str(ErrorCode.PAYMENT_NOT_FOUND): status.HTTP_404_NOT_FOUND,
    str(ErrorCode.PAYMENT_FAILED): status.HTTP_400_BAD_REQUEST,
}

PLAN_ERROR_MAPPING = {
    str(ErrorCode.PLAN_NOT_FOUND): status.HTTP_404_NOT_FOUND,
    str(ErrorCode.PLAN_EXISTS): status.HTTP_409_CONFLICT,
    str(ErrorCode.PLAN_IN_USE): status.HTTP_400_BAD_REQUEST,
}

# 营销模块错误映射
CAMPAIGN_ERROR_MAPPING = {
    str(ErrorCode.CAMPAIGN_NOT_FOUND): status.HTTP_404_NOT_FOUND,
    str(ErrorCode.CAMPAIGN_ENDED): status.HTTP_400_BAD_REQUEST,
    str(ErrorCode.CAMPAIGN_NOT_STARTED): status.HTTP_400_BAD_REQUEST,
    str(ErrorCode.CAMPAIGN_FULL): status.HTTP_400_BAD_REQUEST,
    str(ErrorCode.CAMPAIGN_NOT_ELIGIBLE): status.HTTP_403_FORBIDDEN,
}

INVITATION_ERROR_MAPPING = {
    str(ErrorCode.INVITATION_NOT_FOUND): status.HTTP_404_NOT_FOUND,
    str(ErrorCode.INVITATION_EXPIRED): status.HTTP_400_BAD_REQUEST,
    str(ErrorCode.INVITATION_USED): status.HTTP_409_CONFLICT,
    str(ErrorCode.INVITATION_INVALID): status.HTTP_400_BAD_REQUEST,
    str(ErrorCode.INVITATION_LIMIT_EXCEEDED): status.HTTP_429_TOO_MANY_REQUESTS,
    str(ErrorCode.INVITATION_GENERATE_ERROR): status.HTTP_500_INTERNAL_SERVER_ERROR,
    str(ErrorCode.INVITATION_SAVE_ERROR): status.HTTP_500_INTERNAL_SERVER_ERROR,
    str(ErrorCode.ALREADY_PARTICIPATED): status.HTTP_409_CONFLICT,
}

REWARD_ERROR_MAPPING = {
    # 通用错误
    str(ErrorCode.NOT_FOUND): status.HTTP_404_NOT_FOUND,
    str(ErrorCode.PERMISSION_DENIED): status.HTTP_403_FORBIDDEN,
    str(ErrorCode.INVALID_INPUT): status.HTTP_400_BAD_REQUEST,
    str(ErrorCode.SYSTEM_ERROR): status.HTTP_500_INTERNAL_SERVER_ERROR,
}

# 整合所有营销错误映射
MARKETING_ERROR_MAPPING = {
    **CAMPAIGN_ERROR_MAPPING,
    **INVITATION_ERROR_MAPPING,
    **REWARD_ERROR_MAPPING,
}

# 商品管理模块错误映射
PRODUCT_ERROR_MAPPING = {
    str(ErrorCode.NOT_FOUND): status.HTTP_404_NOT_FOUND,
    str(ErrorCode.RESOURCE_EXISTS): status.HTTP_409_CONFLICT,
    str(ErrorCode.PERMISSION_DENIED): status.HTTP_403_FORBIDDEN,
    str(ErrorCode.INVALID_INPUT): status.HTTP_400_BAD_REQUEST,
    str(ErrorCode.OPERATION_FAILED): status.HTTP_400_BAD_REQUEST,
    str(ErrorCode.INTERNAL_ERROR): status.HTTP_500_INTERNAL_SERVER_ERROR,
}

CATEGORY_ERROR_MAPPING = {
    str(ErrorCode.NOT_FOUND): status.HTTP_404_NOT_FOUND,
    str(ErrorCode.RESOURCE_EXISTS): status.HTTP_409_CONFLICT,
    str(ErrorCode.PERMISSION_DENIED): status.HTTP_403_FORBIDDEN,
    str(ErrorCode.INVALID_INPUT): status.HTTP_400_BAD_REQUEST,
    str(ErrorCode.OPERATION_FAILED): status.HTTP_400_BAD_REQUEST,
    str(ErrorCode.INTERNAL_ERROR): status.HTTP_500_INTERNAL_SERVER_ERROR,
}

PRODUCT_VARIANT_ERROR_MAPPING = {
    str(ErrorCode.NOT_FOUND): status.HTTP_404_NOT_FOUND,
    str(ErrorCode.RESOURCE_EXISTS): status.HTTP_409_CONFLICT,
    str(ErrorCode.PERMISSION_DENIED): status.HTTP_403_FORBIDDEN,
    str(ErrorCode.INVALID_INPUT): status.HTTP_400_BAD_REQUEST,
    str(ErrorCode.OPERATION_FAILED): status.HTTP_400_BAD_REQUEST,
    str(ErrorCode.INTERNAL_ERROR): status.HTTP_500_INTERNAL_SERVER_ERROR,
}

INVENTORY_ERROR_MAPPING = {
    str(ErrorCode.NOT_FOUND): status.HTTP_404_NOT_FOUND,
    str(ErrorCode.RESOURCE_EXISTS): status.HTTP_409_CONFLICT,
    str(ErrorCode.PERMISSION_DENIED): status.HTTP_403_FORBIDDEN,
    str(ErrorCode.INVALID_INPUT): status.HTTP_400_BAD_REQUEST,
    str(ErrorCode.OPERATION_FAILED): status.HTTP_400_BAD_REQUEST,
    str(ErrorCode.INTERNAL_ERROR): status.HTTP_500_INTERNAL_SERVER_ERROR,
}

# 整合所有商品管理错误映射
PRODUCTS_ERROR_MAPPING = {
    **PRODUCT_ERROR_MAPPING,
    **CATEGORY_ERROR_MAPPING,
    **PRODUCT_VARIANT_ERROR_MAPPING,
    **INVENTORY_ERROR_MAPPING,
}

# 图册管理模块错误映射
ALBUM_ERROR_MAPPING = {
    str(ErrorCode.NOT_FOUND): status.HTTP_404_NOT_FOUND,
    str(ErrorCode.RESOURCE_EXISTS): status.HTTP_409_CONFLICT,
    str(ErrorCode.PERMISSION_DENIED): status.HTTP_403_FORBIDDEN,
    str(ErrorCode.INVALID_INPUT): status.HTTP_400_BAD_REQUEST,
    str(ErrorCode.OPERATION_FAILED): status.HTTP_400_BAD_REQUEST,
    str(ErrorCode.INTERNAL_ERROR): status.HTTP_500_INTERNAL_SERVER_ERROR,
}

SHOP_ERROR_MAPPING = {
    str(ErrorCode.NOT_FOUND): status.HTTP_404_NOT_FOUND,
    str(ErrorCode.RESOURCE_EXISTS): status.HTTP_409_CONFLICT,
    str(ErrorCode.PERMISSION_DENIED): status.HTTP_403_FORBIDDEN,
    str(ErrorCode.INVALID_INPUT): status.HTTP_400_BAD_REQUEST,
    str(ErrorCode.OPERATION_FAILED): status.HTTP_400_BAD_REQUEST,
    str(ErrorCode.INTERNAL_ERROR): status.HTTP_500_INTERNAL_SERVER_ERROR,
}

SKU_ERROR_MAPPING = {
    str(ErrorCode.NOT_FOUND): status.HTTP_404_NOT_FOUND,
    str(ErrorCode.RESOURCE_EXISTS): status.HTTP_409_CONFLICT,
    str(ErrorCode.PERMISSION_DENIED): status.HTTP_403_FORBIDDEN,
    str(ErrorCode.INVALID_INPUT): status.HTTP_400_BAD_REQUEST,
    str(ErrorCode.OPERATION_FAILED): status.HTTP_400_BAD_REQUEST,
    str(ErrorCode.INTERNAL_ERROR): status.HTTP_500_INTERNAL_SERVER_ERROR,
}

# 导出所有错误映射常量
__all__ = [
    "handle_route_errors",
    "ResultHandler",
    "create_error_response",
    "AUTH_ERROR_MAPPING",
    "USER_ERROR_MAPPING",
    "ROLE_ERROR_MAPPING",
    "SUBSCRIPTION_ERROR_MAPPING",
    "INVOICE_ERROR_MAPPING",
    "PAYMENT_ERROR_MAPPING",
    "PLAN_ERROR_MAPPING",
    "CAMPAIGN_ERROR_MAPPING",
    "INVITATION_ERROR_MAPPING",
    "REWARD_ERROR_MAPPING",
    "MARKETING_ERROR_MAPPING",
    "PRODUCT_ERROR_MAPPING",
    "CATEGORY_ERROR_MAPPING",
    "PRODUCT_VARIANT_ERROR_MAPPING",
    "INVENTORY_ERROR_MAPPING",
    "PRODUCTS_ERROR_MAPPING",
    "ALBUM_ERROR_MAPPING",
    "SHOP_ERROR_MAPPING",
    "SKU_ERROR_MAPPING",
]