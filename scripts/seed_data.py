"""
数据库种子数据脚本。
用于初始化数据库的基础数据。
"""
import argparse
import asyncio
import json
import logging
import os
import random
import subprocess
import sys
import time
import unittest.mock
from datetime import datetime, timedelta, timezone
from functools import wraps
from pathlib import Path
from typing import Any, Dict, List, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 模拟事件总线，避免"no running event loop"错误
with unittest.mock.patch('asyncio.create_task'):
    from sqlalchemy import insert
    from sqlalchemy.ext.asyncio import AsyncSession
    from sqlalchemy.future import select

    from svc.apps.albums.repositories.album import AlbumRepository
    from svc.apps.albums.repositories.album_image import AlbumImageRepository
    from svc.apps.albums.schemas.album import AlbumCreate
    from svc.apps.albums.schemas.album_image import AlbumImageCreate
    from svc.apps.auth.models import Permission, Role, User, user_role
    from svc.apps.auth.models.wechat_user import WechatUser
    from svc.apps.auth.repositories import RoleRepository, UserRepository
    from svc.apps.billing.models.subscription_plan import SubscriptionPlan
    from svc.apps.billing.repositories import SubscriptionPlanRepository
    from svc.apps.marketing.models.campaign import (AntiAbuseStrategy,
                                                    Campaign, CampaignStatus)
    from svc.apps.marketing.models.invitation import Invitation
    from svc.apps.marketing.models.reward import (RewardRecord, RewardStrategy,
                                                  RewardType)
    from svc.apps.marketing.repositories import (CampaignRepository,
                                                 InvitationRepository,
                                                 RewardRecordRepository,
                                                 RewardStrategyRepository)
    from svc.apps.products.repositories.category import CategoryRepository
    from svc.apps.products.repositories.inventory import InventoryRepository
    from svc.apps.products.repositories.product import ProductRepository
    from svc.apps.products.repositories.sku import ProductSKURepository
    from svc.apps.products.repositories.spec import (SpecOptionRepository,
                                                     SpecRepository)
    from svc.apps.products.schemas.category import CategoryCreate
    from svc.apps.products.schemas.inventory import InventoryCreate
    from svc.apps.products.schemas.product import (ProductCreate,
                                                   ProductSKUCreate)
    from svc.apps.products.schemas.spec import SpecCreate, SpecOptionCreate
    from svc.apps.shops.models.shop import ShopStatus
    from svc.apps.shops.repositories.shop import ShopRepository
    from svc.apps.shops.schemas.shop import ShopCreate
    from svc.core.database.session import get_session
    from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

# 创建日志
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('seed_data')

def performance_monitor(operation_name: str):
    """性能监控装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            logger.info(f"开始执行 {operation_name}...")
            try:
                result = await func(*args, **kwargs)
                end_time = time.time()
                execution_time = end_time - start_time
                logger.info(f"{operation_name} 完成，耗时: {execution_time:.2f} 秒")
                return result
            except Exception as e:
                end_time = time.time()
                execution_time = end_time - start_time
                logger.error(f"{operation_name} 失败，耗时: {execution_time:.2f} 秒，错误: {str(e)}")
                raise
        return wrapper
    return decorator

def run_command(command, check=True):
    """运行命令并打印输出"""
    print(f"执行命令: {command}")
    result = subprocess.run(command, shell=True, check=check)
    return result


def reset_database(recreate=False):
    """重置数据库
    Args:
        recreate: 是否重新创建数据库，如果为True，将删除并重新创建数据库；
                 如果为False，将只回滚并重新应用迁移
    """
    if recreate:
        # 获取数据库连接信息
        db_name = os.getenv("POSTGRES_DB", "fastapi_nano")
        db_user = os.getenv("POSTGRES_USER", "postgres")
        db_password = os.getenv("POSTGRES_PASSWORD", "postgres")
        db_server = os.getenv("POSTGRES_SERVER", "localhost")
        # 删除数据库
        print(f"删除数据库 {db_name}...")
        run_command(f"PGPASSWORD={db_password} dropdb -h {db_server} -U {db_user} {db_name}", check=False)
        # 创建数据库
        print(f"创建数据库 {db_name}...")
        run_command(f"PGPASSWORD={db_password} createdb -h {db_server} -U {db_user} {db_name}")
    else:
        # 回滚所有迁移
        print("回滚所有迁移...")
        run_command("alembic downgrade base")
    # 应用所有迁移
    print("应用所有迁移...")
    run_command("alembic upgrade head")
    print("数据库重置完成！")

async def create_permissions(db: AsyncSession) -> List[Permission]:
    """创建权限数据"""
    # 先检查是否已存在
    result = await db.execute(select(Permission).where(Permission.name == "user:read"))
    existing = result.scalars().first()
    if existing:
        logger.info("权限数据已存在，跳过创建")
        # 返回所有现有权限
        result = await db.execute(select(Permission))
        return result.scalars().all()
    
    logger.info("创建权限数据...")
    permissions_data = [
        # 全局权限
        {"name": "*:*", "description": "所有权限（超级管理员）"},
        
        # 认证授权模块权限
        {"name": "user:read", "description": "查看用户信息"},
        {"name": "user:create", "description": "创建用户"},
        {"name": "user:update", "description": "更新用户信息"},
        {"name": "user:delete", "description": "删除用户"},
        {"name": "user:manage", "description": "管理用户（禁用/启用账户等）"},
        {"name": "role:read", "description": "查看角色信息"},
        {"name": "role:create", "description": "创建角色"},
        {"name": "role:update", "description": "更新角色"},
        {"name": "role:delete", "description": "删除角色"},
        {"name": "role:assign", "description": "分配角色给用户"},
        {"name": "permission:read", "description": "查看权限信息"},
        {"name": "permission:assign", "description": "给角色分配权限"},
        
        # 计费/订阅模块权限
        {"name": "subscription_plan:read", "description": "查看订阅计划"},
        {"name": "subscription_plan:create", "description": "创建订阅计划"},
        {"name": "subscription_plan:update", "description": "更新订阅计划"},
        {"name": "subscription_plan:delete", "description": "删除订阅计划"},
        {"name": "subscription:read", "description": "查看订阅信息"},
        {"name": "subscription:create", "description": "创建订阅"},
        {"name": "subscription:update", "description": "更新订阅"},
        {"name": "subscription:cancel", "description": "取消订阅"},
        {"name": "subscription:renew", "description": "续订订阅"},
        {"name": "invoice:read", "description": "查看发票"},
        {"name": "invoice:create", "description": "创建发票"},
        {"name": "invoice:update", "description": "更新发票"},
        {"name": "invoice:delete", "description": "删除发票"},
        {"name": "payment:read", "description": "查看支付信息"},
        {"name": "payment:process", "description": "处理支付"},
        {"name": "payment:refund", "description": "退款操作"},
        
        # 营销模块权限
        {"name": "campaign:read", "description": "查看营销活动"},
        {"name": "campaign:create", "description": "创建营销活动"},
        {"name": "campaign:update", "description": "更新营销活动"},
        {"name": "campaign:delete", "description": "删除营销活动"},
        {"name": "campaign:activate", "description": "激活/停用营销活动"},
        {"name": "invitation:read", "description": "查看邀请信息"},
        {"name": "invitation:create", "description": "创建邀请"},
        {"name": "invitation:delete", "description": "删除邀请"},
        {"name": "reward_strategy:read", "description": "查看奖励策略"},
        {"name": "reward_strategy:create", "description": "创建奖励策略"},
        {"name": "reward_strategy:update", "description": "更新奖励策略"},
        {"name": "reward_strategy:delete", "description": "删除奖励策略"},
        {"name": "reward_record:read", "description": "查看奖励记录"},
        {"name": "reward_record:create", "description": "创建奖励记录"},
        {"name": "reward_record:issue", "description": "发放奖励"},
        
        # 系统模块权限
        {"name": "config:read", "description": "查看系统配置"},
        {"name": "config:update", "description": "更新系统配置"},
        {"name": "audit_log:read", "description": "查看审计日志"},
        {"name": "audit_log:delete", "description": "删除审计日志"},
        {"name": "system:backup", "description": "系统备份操作"},
        {"name": "system:restore", "description": "系统恢复操作"},
        {"name": "system:maintenance", "description": "系统维护模式切换"},
        {"name": "system:monitor", "description": "系统监控访问权限"}
    ]
    
    permissions = []
    for perm_data in permissions_data:
        perm = Permission(**perm_data)
        db.add(perm)
        permissions.append(perm)
    
    await db.commit()
    logger.info(f"创建了{len(permissions)}个权限")
    return permissions

async def create_roles(db: AsyncSession, permissions: List[Permission]) -> List[Role]:
    """创建角色数据"""
    # 先检查是否已存在
    result = await db.execute(select(Role).where(Role.name == "admin"))
    existing = result.scalars().first()
    if existing:
        logger.info("角色数据已存在，跳过创建")
        # 返回所有现有角色
        result = await db.execute(select(Role))
        return result.scalars().all()
    
    logger.info("创建角色数据...")
    roles_data = [
        {
            "name": "admin",
            "description": "系统管理员",
            "is_system": True,
            "permissions": permissions  # 管理员拥有所有权限
        },
        {
            "name": "user",
            "description": "普通用户",
            "is_system": True,
            "permissions": [p for p in permissions if p.name.startswith("user:read")]
        },
        {
            "name": "vip",
            "description": "VIP用户",
            "is_system": False,
            "permissions": [p for p in permissions if ":read" in p.name]
        }
    ]
    
    roles = []
    for role_data in roles_data:
        role_permissions = role_data.pop("permissions")
        role = Role(**role_data)
        role.permissions = role_permissions
        db.add(role)
        roles.append(role)
    
    await db.commit()
    logger.info(f"创建了{len(roles)}个角色")
    return roles

async def create_users(db: AsyncSession, roles: List[Role]) -> List[User]:
    """创建用户数据"""
    logger.info("开始创建用户数据...")
    users_data = [
        {
            "email": "<EMAIL>",
            "password": "qinjun666",
            "username": "super",
            "fullname": "系统管理员",
            "is_superuser": True,
            "roles": [roles[0]]  # admin角色
        },
        {
            "email": "<EMAIL>",
            "password": "user123",
            "username": "user1",
            "fullname": "测试用户1",
            "roles": [roles[1]]  # user角色
        },
        {
            "email": "<EMAIL>",
            "password": "user123",
            "username": "user2",
            "fullname": "测试用户2",
            "roles": [roles[1], roles[2]]  # user + vip角色
        }
    ]
    
    users = []
    for user_data in users_data:
        # 检查用户是否已存在
        email = user_data["email"]
        result = await db.execute(select(User).where(User.email == email))
        existing = result.scalars().first()
        
        if existing:
            logger.info(f"用户 {email} 已存在，跳过创建")
            users.append(existing)
            continue
        
        # 创建新用户
        user_roles = user_data.pop("roles")
        user = await UserRepository(db=db).create(
            **user_data
        )
        
        # 设置用户角色关系
        for role in user_roles:
            stmt = insert(user_role).values(user_id=user.id, role_id=role.id)
            await db.execute(stmt)
        
        logger.info(f"成功创建用户: {email}")
        users.append(user)
    
    await db.commit()
    logger.info(f"用户创建过程完成，共有 {len(users)} 个用户")
    return users


async def create_subscription_plans(db: AsyncSession, creator_id: int) -> List[SubscriptionPlan]:
    """
    创建订阅计划数据
    
    Args:
        db: 数据库会话
        creator_id: 创建者ID，默认为1(管理员)
    
    Returns:
        List[SubscriptionPlan]: 创建的订阅计划列表
    """
    try:
        # 先检查是否已存在
        result = await db.execute(select(SubscriptionPlan).where(SubscriptionPlan.name == "基础版"))
        existing = result.scalars().first()
        if existing:
            logger.info("订阅计划数据已存在，跳过创建")
            # 返回所有现有订阅计划
            result = await db.execute(select(SubscriptionPlan))
            return result.scalars().all()
        
        # 验证creator_id是否存在
        user_check = await db.execute(select(User).where(User.id == creator_id))
        user = user_check.scalars().first()
        if not user:
            raise ValueError(f"用户ID {creator_id} 不存在，无法创建订阅计划")
        
        logger.info(f"创建订阅计划数据，创建者ID: {creator_id}...")
        
        plans_data = [
            {
                "name": "基础版",
                "description": "适合个人用户",
                "price": 99.00,
                "currency": "CNY",
                "interval": "month",
                "tier": "basic",
                "max_users": 1,
                "max_storage": 1024,  # 1GB
                "max_projects": 3,
                "features": {"api_calls": 1000, "support": "email"},
                "user_id": creator_id
            },
            {
                "name": "专业版",
                "description": "适合小型团队",
                "price": 299.00,
                "currency": "CNY",
                "interval": "month",
                "tier": "premium",
                "max_users": 5,
                "max_storage": 5120,  # 5GB
                "max_projects": 10,
                "features": {"api_calls": 5000, "support": "priority"},
                "user_id": creator_id
            },
            {
                "name": "企业版",
                "description": "适合大型企业",
                "price": 999.00,
                "currency": "CNY",
                "interval": "month",
                "tier": "enterprise",
                "max_users": 20,
                "max_storage": 20480,  # 20GB
                "max_projects": 50,
                "features": {"api_calls": "unlimited", "support": "24/7"},
                "user_id": creator_id
            }
        ]
        
        plans = []
        plan_repo = SubscriptionPlanRepository(db=db)
        for idx, plan_data in enumerate(plans_data, 1):
            try:
                # 使用仓库模式创建订阅计划，确保数据一致性
                plan = await plan_repo.create(
                    user_id=plan_data["user_id"],
                    name=plan_data["name"],
                    description=plan_data.get("description"),
                    price=plan_data["price"],
                    currency=plan_data.get("currency", "CNY"),
                    interval=plan_data.get("interval", "month"),
                    features=plan_data.get("features", {}),
                    tier=plan_data.get("tier"),
                    max_users=plan_data.get("max_users"),
                    max_storage=plan_data.get("max_storage"),
                    max_projects=plan_data.get("max_projects")
                )
                logger.info(f"创建订阅计划 {idx}/{len(plans_data)}: {plan.name} (ID: {plan.id})")
                plans.append(plan)
            except Exception as e:
                logger.error(f"创建订阅计划 '{plan_data['name']}' 失败: {str(e)}")
                raise
        
        await db.commit()
        logger.info(f"成功创建了{len(plans)}个订阅计划")
        return plans
    except Exception as e:
        logger.error(f"创建订阅计划时发生错误: {str(e)}", exc_info=True)
        await db.rollback()
        raise

async def create_campaigns(session: AsyncSession, admin_user_id: int) -> list[Campaign]:
    """
    创建营销活动。
    """
    logging.info("开始创建营销活动...")
    try:
        campaign_repo = CampaignRepository(db=session)
        # 检查是否已存在
        campaigns, total = await campaign_repo.get_campaigns()
        if total > 0:
            logging.info(f"营销活动已存在（共{total}个），跳过创建")
            return campaigns
        campaign_ids = []
        
        # 创建邀请新用户活动
        invite_campaign = Campaign(
            name="邀请新用户",
            description="邀请新用户注册并使用我们的服务",
            start_date=get_utc_now_without_tzinfo(),
            end_date=get_utc_now_without_tzinfo() + timedelta(days=180),
            status=CampaignStatus.ACTIVE,
            creator_id=admin_user_id,
            anti_abuse_strategy=AntiAbuseStrategy.BOTH
        )
        # 直接添加到会话，而不是使用repository的create方法
        session.add(invite_campaign)
        await session.flush()
        campaign_ids.append(invite_campaign.id)
        logging.info(f"创建营销活动成功: {invite_campaign.name}, ID: {invite_campaign.id}")
        
        # 创建首次购买奖励活动
        first_purchase_campaign = Campaign(
            name="首次购买奖励",
            description="奖励首次在平台上购买的用户",
            start_date=get_utc_now_without_tzinfo(),
            end_date=get_utc_now_without_tzinfo() + timedelta(days=90),
            status=CampaignStatus.ACTIVE,
            creator_id=admin_user_id,
            anti_abuse_strategy=AntiAbuseStrategy.BOTH
        )
        # 直接添加到会话，而不是使用repository的create方法
        session.add(first_purchase_campaign)
        await session.flush()
        campaign_ids.append(first_purchase_campaign.id)
        logging.info(f"创建营销活动成功: {first_purchase_campaign.name}, ID: {first_purchase_campaign.id}")
        
        # 创建VIP会员专享活动
        vip_campaign = Campaign(
            name="VIP会员专享",
            description="VIP会员专享活动与优惠",
            start_date=get_utc_now_without_tzinfo(),
            end_date=get_utc_now_without_tzinfo() + timedelta(days=120),
            status=CampaignStatus.ACTIVE,
            creator_id=admin_user_id,
            anti_abuse_strategy=AntiAbuseStrategy.BOTH
        )
        # 直接添加到会话，而不是使用repository的create方法
        session.add(vip_campaign)
        await session.flush()
        campaign_ids.append(vip_campaign.id)
        logging.info(f"创建营销活动成功: {vip_campaign.name}, ID: {vip_campaign.id}")
        
        logging.info(f"成功创建 {len(campaign_ids)} 个营销活动")
        return campaign_ids
    except Exception as e:
        logging.error(f"创建营销活动失败: {str(e)}")
        raise

async def create_reward_strategies(db: AsyncSession, campaigns: List[int], creator_id: int) -> List[RewardStrategy]:
    """
    创建奖励策略数据
    
    Args:
        db: 数据库会话
        campaigns: 活动列表
        creator_id: 创建者ID
    
    Returns:
        List[RewardStrategy]: 创建的奖励策略列表
    """
    try:
        # 先检查是否已存在
        result = await db.execute(select(RewardStrategy).where(RewardStrategy.name == "邀请人奖励"))
        existing = result.scalars().first()
        if existing:
            logger.info("奖励策略数据已存在，跳过创建")
            # 返回所有现有策略
            result = await db.execute(select(RewardStrategy))
            return result.scalars().all()
        
        if not campaigns or len(campaigns) < 3:
            raise ValueError(f"需要至少3个营销活动来创建奖励策略，当前只有{len(campaigns) if campaigns else 0}个")
        
        logger.info("创建奖励策略数据...")
        
        # 初始化策略仓库
        reward_strategy_repo = RewardStrategyRepository(db=db)
        
        # 为每个活动创建奖励策略
        reward_strategies_data = [
            # 活动1的策略
            {
                "campaign_id": campaigns[0],
                "name": "邀请人奖励",
                "description": "邀请好友注册成功后，邀请人获得的奖励",
                "reward_type": RewardType.FIXED,
                "is_for_inviter": True,
                "is_for_invitee": False,
                "base_reward": 50.0,  # 固定奖励50元
                "min_invitations": 1,
                "max_rewards": 5
            },
            {
                "campaign_id": campaigns[0],
                "name": "受邀人奖励",
                "description": "被邀请注册成功后，受邀人获得的奖励",
                "reward_type": RewardType.FIXED,
                "is_for_inviter": False,
                "is_for_invitee": True,
                "base_reward": 20.0,  # 固定奖励20元
                "min_invitations": None,
                "max_rewards": 1
            },
            # 活动2的策略
            {
                "campaign_id": campaigns[1],
                "name": "春季推广邀请奖励",
                "description": "春季推广活动邀请奖励",
                "reward_type": RewardType.TIERED,
                "is_for_inviter": True,
                "is_for_invitee": False,
                "base_reward": 30.0,
                "tiered_config": json.dumps([
                    {"threshold": 3, "reward": 50.0},
                    {"threshold": 5, "reward": 100.0},
                    {"threshold": 10, "reward": 200.0}
                ]),
                "min_invitations": 1,
                "max_rewards": 10
            },
            {
                "campaign_id": campaigns[1],
                "name": "春季推广受邀奖励",
                "description": "春季推广活动受邀奖励",
                "reward_type": RewardType.FIXED,
                "is_for_inviter": False,
                "is_for_invitee": True,
                "base_reward": 30.0,
                "min_invitations": None,
                "max_rewards": 1
            },
            # 活动3的策略
            {
                "campaign_id": campaigns[2],
                "name": "VIP会员专享奖励",
                "description": "VIP专享活动奖励",
                "reward_type": RewardType.PERCENTAGE,
                "is_for_inviter": True,
                "is_for_invitee": True,
                "base_reward": 10.0,
                "percentage_rate": 15.0,  # 15%的额外奖励
                "min_invitations": 1,
                "max_rewards": 3
            }
        ]
        
        reward_strategies = []
        for idx, strategy_data in enumerate(reward_strategies_data, 1):
            try:
                # 验证campaign_id
                campaign_id = strategy_data["campaign_id"]
                campaign_check = await db.execute(select(Campaign).where(Campaign.id == campaign_id))
                if not campaign_check.scalars().first():
                    logger.warning(f"策略 '{strategy_data['name']}' 引用的活动ID {campaign_id} 不存在，跳过创建")
                    continue
                
                # 使用RewardStrategyRepository创建策略
                strategy = await reward_strategy_repo.create_strategy(
                    campaign_id=strategy_data["campaign_id"],
                    name=strategy_data["name"],
                    description=strategy_data.get("description"),
                    reward_type=strategy_data["reward_type"],
                    is_for_inviter=strategy_data["is_for_inviter"],
                    is_for_invitee=strategy_data["is_for_invitee"],
                    base_reward=strategy_data["base_reward"],
                    percentage_rate=strategy_data.get("percentage_rate"),
                    tiered_config=strategy_data.get("tiered_config"),
                    min_invitations=strategy_data.get("min_invitations"),
                    max_rewards=strategy_data.get("max_rewards")
                )
                
                logger.info(f"创建奖励策略 {idx}/{len(reward_strategies_data)}: {strategy.name} (ID: {strategy.id})")
                reward_strategies.append(strategy)
            except Exception as e:
                logger.error(f"创建奖励策略 '{strategy_data['name']}' 失败: {str(e)}")
                raise
        
        await db.commit()
        logger.info(f"成功创建了{len(reward_strategies)}个奖励策略")
        return reward_strategies
    except Exception as e:
        logger.error(f"创建奖励策略时发生错误: {str(e)}", exc_info=True)
        await db.rollback()
        raise

async def create_invitations(db: AsyncSession, campaigns: List[Campaign], users: List[User]) -> List[Invitation]:
    """
    创建邀请记录数据
    
    Args:
        db: 数据库会话
        campaigns: 活动列表
        users: 用户列表
    
    Returns:
        List[Invitation]: 创建的邀请记录列表
    """
    try:
        # 先检查是否已存在
        result = await db.execute(select(Invitation).where(Invitation.code == "INV123456"))
        existing = result.scalars().first()
        if existing:
            logger.info("邀请记录数据已存在，跳过创建")
            # 返回所有现有邀请
            result = await db.execute(select(Invitation))
            return result.scalars().all()
        
        # 验证输入数据
        if not campaigns or len(campaigns) < 2:
            raise ValueError(f"需要至少2个营销活动来创建邀请记录，当前只有{len(campaigns) if campaigns else 0}个")
        
        if not users or len(users) < 3:
            raise ValueError(f"需要至少3个用户来创建邀请记录，当前只有{len(users) if users else 0}个")
        
        logger.info("创建邀请记录数据...")
        
        now = get_utc_now_without_tzinfo()
        
        # 创建邀请记录
        invitations_data = [
            # user1邀请user2(已使用)
            {
                "campaign_id": campaigns[0],
                "inviter_id": users[1].id,  # user1
                "invitee_id": users[2].id,  # user2
                "code": "INV123456",
                "is_used": True,
                "used_at": now - timedelta(days=15),
                "invitee_ip": "*************",
                "invitee_device": "iPhone 13",
                "opened_count": 2
            },
            # user2邀请其他用户(未使用)
            {
                "campaign_id": campaigns[0],
                "inviter_id": users[2].id,  # user2
                "invitee_id": None,  # 未被使用
                "code": "INV234567",
                "is_used": False,
                "used_at": None,
                "invitee_ip": None,
                "invitee_device": None,
                "opened_count": 5
            },
            # user1在另一个活动中的邀请
            {
                "campaign_id": campaigns[1],
                "inviter_id": users[1].id,  # user1
                "invitee_id": None,  # 未被使用
                "code": "SPR123456",
                "is_used": False,
                "used_at": None,
                "invitee_ip": None,
                "invitee_device": None,
                "opened_count": 3
            }
        ]
        
        invitations = []
        for idx, invitation_data in enumerate(invitations_data, 1):
            try:
                # 验证外键ID是否存在
                campaign_id = invitation_data["campaign_id"]
                campaign_check = await db.execute(select(Campaign).where(Campaign.id == campaign_id))
                if not campaign_check.scalars().first():
                    logger.warning(f"邀请记录 #{idx} 引用的活动ID {campaign_id} 不存在，跳过创建")
                    continue
                
                inviter_id = invitation_data["inviter_id"]
                inviter_check = await db.execute(select(User).where(User.id == inviter_id))
                if not inviter_check.scalars().first():
                    logger.warning(f"邀请记录 #{idx} 引用的邀请人ID {inviter_id} 不存在，跳过创建")
                    continue
                
                # 如果有被邀请人，也检查ID
                invitee_id = invitation_data["invitee_id"]
                if invitee_id is not None:
                    invitee_check = await db.execute(select(User).where(User.id == invitee_id))
                    if not invitee_check.scalars().first():
                        logger.warning(f"邀请记录 #{idx} 引用的被邀请人ID {invitee_id} 不存在，跳过创建")
                        continue
                
                invitation = Invitation(**invitation_data)
                db.add(invitation)
                await db.flush()
                logger.info(f"创建邀请记录 {idx}/{len(invitations_data)}: {invitation.code} (ID: {invitation.id})")
                invitations.append(invitation)
            except Exception as e:
                logger.error(f"创建邀请记录 #{idx} 失败: {str(e)}")
                raise
        
        await db.commit()
        logger.info(f"成功创建了{len(invitations)}个邀请记录")
        return invitations
    except Exception as e:
        logger.error(f"创建邀请记录时发生错误: {str(e)}", exc_info=True)
        await db.rollback()
        raise

async def create_reward_records(db: AsyncSession, campaigns: List[Campaign], users: List[User], invitations: List[Invitation], reward_strategies: List[RewardStrategy]) -> List[RewardRecord]:
    """
    创建奖励记录数据
    
    Args:
        db: 数据库会话
        campaigns: 活动列表
        users: 用户列表
        invitations: 邀请记录列表
        reward_strategies: 奖励策略列表
    
    Returns:
        List[RewardRecord]: 创建的奖励记录列表
    """
    try:
        # 先检查是否已存在
        if not invitations or not invitations[0]:
            raise ValueError("缺少邀请记录，无法创建奖励记录")
        
        # 如果至少有一个邀请记录，检查是否已存在奖励记录
        check_invitation = invitations[0]
        result = await db.execute(select(RewardRecord).where(
            (RewardRecord.campaign_id == check_invitation.campaign_id) & 
            (RewardRecord.user_id == check_invitation.inviter_id)
        ))
        existing = result.scalars().first()
        if existing:
            logger.info("奖励记录数据已存在，跳过创建")
            # 返回所有现有奖励记录
            result = await db.execute(select(RewardRecord))
            return result.scalars().all()
        
        # 验证输入数据
        if not campaigns or len(campaigns) < 2:
            raise ValueError(f"需要至少2个营销活动来创建奖励记录，当前只有{len(campaigns) if campaigns else 0}个")
        
        if not users or len(users) < 2:
            raise ValueError(f"需要至少2个用户来创建奖励记录，当前只有{len(users) if users else 0}个")
        
        if not reward_strategies or len(reward_strategies) < 3:
            raise ValueError(f"需要至少3个奖励策略来创建奖励记录，当前只有{len(reward_strategies) if reward_strategies else 0}个")
        
        logger.info("创建奖励记录数据...")
        
        now = get_utc_now_without_tzinfo()
        
        # 获取已使用的邀请
        used_invitation = None
        for inv in invitations:
            if inv.is_used:
                used_invitation = inv
                break
        
        if not used_invitation:
            logger.warning("没有找到已使用的邀请记录，将使用第一个邀请记录")
            used_invitation = invitations[0]
        
        reward_records_data = [
            # 邀请人的奖励(已发放)
            {
                "campaign_id": used_invitation.campaign_id,
                "invitation_id": used_invitation.id,
                "strategy_id": reward_strategies[0].id,  # 对应"邀请人奖励"策略
                "user_id": used_invitation.inviter_id,
                "reward_type": "cash",
                "reward_value": 50.0,
                "reward_description": "成功邀请好友注册",
                "is_issued": True,
                "issued_at": now - timedelta(days=14)
            },
            # 被邀请人的奖励(已发放)
            {
                "campaign_id": used_invitation.campaign_id,
                "invitation_id": used_invitation.id,
                "strategy_id": reward_strategies[1].id,  # 对应"受邀人奖励"策略
                "user_id": used_invitation.invitee_id if used_invitation.invitee_id else users[2].id,
                "reward_type": "cash",
                "reward_value": 20.0,
                "reward_description": "接受邀请注册",
                "is_issued": True,
                "issued_at": now - timedelta(days=14)
            },
            # 另一个活动中的奖励(未发放)
            {
                "campaign_id": campaigns[1],
                "invitation_id": None,
                "strategy_id": reward_strategies[2].id,  # 对应"春季推广邀请奖励"策略
                "user_id": users[1].id,  # user1
                "reward_type": "cash",
                "reward_value": 30.0,
                "reward_description": "春季活动特别奖励",
                "is_issued": False,
                "issued_at": None
            }
        ]
        
        reward_records = []
        for idx, record_data in enumerate(reward_records_data, 1):
            try:
                # 验证外键ID
                campaign_id = record_data["campaign_id"]
                campaign_check = await db.execute(select(Campaign).where(Campaign.id == campaign_id))
                if not campaign_check.scalars().first():
                    logger.warning(f"奖励记录 #{idx} 引用的活动ID {campaign_id} 不存在，跳过创建")
                    continue
                
                if record_data["invitation_id"]:
                    invitation_id = record_data["invitation_id"]
                    invitation_check = await db.execute(select(Invitation).where(Invitation.id == invitation_id))
                    if not invitation_check.scalars().first():
                        logger.warning(f"奖励记录 #{idx} 引用的邀请ID {invitation_id} 不存在，跳过创建")
                        continue
                
                strategy_id = record_data["strategy_id"]
                strategy_check = await db.execute(select(RewardStrategy).where(RewardStrategy.id == strategy_id))
                if not strategy_check.scalars().first():
                    logger.warning(f"奖励记录 #{idx} 引用的策略ID {strategy_id} 不存在，跳过创建")
                    continue
                
                user_id = record_data["user_id"]
                user_check = await db.execute(select(User).where(User.id == user_id))
                if not user_check.scalars().first():
                    logger.warning(f"奖励记录 #{idx} 引用的用户ID {user_id} 不存在，跳过创建")
                    continue
                
                record = RewardRecord(**record_data)
                db.add(record)
                await db.flush()
                logger.info(f"创建奖励记录 {idx}/{len(reward_records_data)}: {record.reward_description} (ID: {record.id})")
                reward_records.append(record)
            except Exception as e:
                logger.error(f"创建奖励记录 #{idx} 失败: {str(e)}")
                raise
        
        await db.commit()
        logger.info(f"成功创建了{len(reward_records)}个奖励记录")
        return reward_records
    except Exception as e:
        logger.error(f"创建奖励记录时发生错误: {str(e)}", exc_info=True)
        await db.rollback()
        raise

async def create_albums(db: AsyncSession) -> List:
    """创建图册数据"""
    try:
        repo = AlbumRepository(db)
        # 检查是否已存在
        albums, total = await repo.get_albums()
        if total > 0:
            logger.info("图册数据已存在，跳过创建")
            return albums
        logger.info("创建图册数据...")
        albums_data = [
            AlbumCreate(
                name="主图册",
                description="商品主图",
                tags=["main", "product"],
                cover_image_id=None,
                status="active",
                sort_order=1,
                meta_data={"scene": "product"}
            ),
            AlbumCreate(
                name="活动图册",
                description="营销活动相关图片",
                tags=["marketing"],
                cover_image_id=None,
                status="active",
                sort_order=2,
                meta_data={"scene": "marketing"}
            )
        ]
        created = []
        for album in albums_data:
            created_album = await repo.create(album.model_dump())
            created.append(created_album)
            logger.info(f"创建图册: {album.name}")
        await db.commit()
        logger.info(f"成功创建{len(created)}个图册")
        return created
    except Exception as e:
        logger.error(f"创建图册时发生错误: {str(e)}", exc_info=True)
        await db.rollback()
        raise

async def create_categories(db: AsyncSession) -> list:
    """创建头盔分类数据（6类）"""
    try:
        repo = CategoryRepository(db)
        categories, total = await repo.get_categories()
        # 检查是否已存在6个头盔分类
        helmet_types = [
            "安全帽", "骑行盔", "摩托盔", "滑雪盔", "攀岩盔", "马术盔"
        ]
        exist_names = [cat.name for cat in categories]
        to_create = [name for name in helmet_types if name not in exist_names]
        created = []
        for idx, name in enumerate(to_create):
            cat = await repo.create(CategoryCreate(
                name=name,
                description=f"{name}类头盔产品",
                slug=f"helmet-{idx+1}",
            ).model_dump())
            created.append(cat)
            logger.info(f"创建头盔分类: {name}")
        await db.commit()
        # 重新获取所有分类，返回头盔分类对象列表
        all_cats, _ = await repo.get_categories()
        helmet_cats = [cat for cat in all_cats if cat.name in helmet_types]
        logger.info(f"成功创建/获取{len(helmet_cats)}个头盔分类")
        return helmet_cats
    except Exception as e:
        logger.error(f"创建分类时发生错误: {str(e)}", exc_info=True)
        await db.rollback()
        raise

async def create_products(db: AsyncSession, categories: list) -> List:
    """批量创建产品数据，每个URL对应一个产品，name作为分类"""
    try:
        repo = ProductRepository(db)
        products, total = await repo.get_products()
        if total > 0:
            logger.info("商品数据已存在，跳过创建")
            return products
        logger.info("批量创建产品数据...")
        # 只保留本地模拟数据生成逻辑
        png_image_urls = [{
            "name": "反光衣",
            "urls": ["http://res.yqbaijiu.com/20250626/9687fd13f22e41a795cbfad364726c61.png",
            "http://res.yqbaijiu.com/20250626/35636cd262dc4bc5bf36f34cc52cc5c6.png",]
        },{
            "name": "安全帽",
            "urls": ["http://res.yqbaijiu.com/20250626/c1db7d903cba4ef69780226fc806726a.png",
            "http://res.yqbaijiu.com/20250626/041043e139424cb495f0bf9883117935.png",
            "http://res.yqbaijiu.com/20250626/2bfcd3d75bb24f33892165ccba2d288e.png",
            "http://res.yqbaijiu.com/20250626/b22092daafab42a4a793485880a24499.png",
            "http://res.yqbaijiu.com/20250626/64cd99e7752544e4907685f6403af05f.png",
            ]}
        ]
        created = []
        album_repo = AlbumRepository(db)
        album_image_repo = AlbumImageRepository(db)
        # 只遍历本地模拟数据
        for category_data in png_image_urls:
            category_name = category_data["name"]
            urls = category_data["urls"]
            # 查找对应的分类
            category = None
            for cat in categories:
                if cat.name == category_name:
                    category = cat
                    break
            if not category:
                logger.warning(f"未找到分类: {category_name}，跳过该分类的产品创建")
                continue
            # 为每个URL创建一个产品
            for idx, url in enumerate(urls):
                product_name = f"{category_name}{idx+1}"
                sku = f"{category_name[:2].upper()}{idx+1:02d}"
                # 创建专属图册
                album = await album_repo.create(AlbumCreate(
                    name=f"{product_name}图册",
                    description=f"{product_name}的主图册",
                    tags=[category_name],
                    status="active",
                    sort_order=len(created)+1,
                    meta_data={"scene": "product"}
                ).model_dump())
                # 为图册添加PNG透明底图片
                cover_image_id = None
                img = await album_image_repo.create(AlbumImageCreate(
                    album_id=album.id,
                    url=url,
                    file_name=url.split("/")[-1],
                    file_size=0,
                    width=800,
                    height=800,
                    mime_type="image/png",
                    is_cover=True,
                    sort_order=1,
                    status="active",
                    meta_data={"scene": "product"}
                ).model_dump())
                cover_image_id = img.id
                # 设置封面ID
                if cover_image_id:
                    album.cover_image_id = cover_image_id
                    db.add(album)
                # 创建产品
                product = await repo.create(ProductCreate(
                    name=product_name,
                    description=f"{category_name}高品质产品，适合多场景使用。",
                    short_description=f"优质{category_name}，安全舒适。",
                    sku=sku,
                    barcode=f"{sku}BAR{idx+1:03d}",
                    category_id=category.id,
                    price=int((199.0 + idx*20) * 100),  # 转换为分
                    cost_price=int((120.0 + idx*10) * 100),  # 转换为分
                    market_price=int((299.0 + idx*30) * 100),  # 转换为分
                    currency="CNY",
                    is_featured=(idx==0),
                    is_digital=False,
                    track_inventory=True,
                    stock_quantity=50 + idx*10,
                    min_stock_level=5,
                    max_stock_level=200,
                    weight=0.45 + idx*0.05,
                    dimensions={"length": 25+idx, "width": 20+idx, "height": 15+idx},
                    attributes={"类型": category_name},
                    seo_title=f"{product_name} - 专业{category_name}商城",
                    seo_description=f"{category_name}，安全舒适，适合多场景使用。",
                    seo_keywords=f"{category_name},安全,防护",
                    rich_description=f'<p>{category_name}，高品质产品，适合多场景使用。</p>',
                    album_id=album.id
                ).model_dump())
                created.append(product)
                logger.info(f"创建产品: {product_name} (分类: {category_name})")
        await db.commit()
        logger.info(f"成功创建{len(created)}个产品")
        return created
    except Exception as e:
        logger.error(f"创建产品时发生错误: {str(e)}", exc_info=True)
        await db.rollback()
        raise

async def create_shops(db: AsyncSession) -> List:
    """批量创建10个门店，每个门店专属图册和图片（随机网络图片）"""
    try:
        repo = ShopRepository(db)
        from svc.apps.shops.schemas.shop import GetShopsParams
        params = GetShopsParams(page_num=1, page_size=1)
        shops, total = await repo.get_shops(params)
        if total >= 10:
            logger.info("门店数据已存在，跳过创建")
            return shops
        logger.info("批量创建门店数据...")
        album_repo = AlbumRepository(db)
        album_image_repo = AlbumImageRepository(db)
        shop_names = [f"测试门店{i+1}" for i in range(10)]
        created = []
        for idx, name in enumerate(shop_names):
            shop = await repo.create(ShopCreate(
                name=name,
                description=f"{name}，优质服务，欢迎光临！",
                address_line1=f"测试路{100+idx}号",
                address_line2=None,
                city="测试市",
                state_province="测试省",
                postal_code=f"1000{idx}",
                country="CN",
                phone_number=f"010-8888{1000+idx}",
                email=f"shop{idx+1}@example.com",
                website=f"https://shop.example.com/{idx+1}",
                opening_hours={"Mon-Sun": "9am-9pm"},
                is_franchise=bool(idx%2),
                latitude=str(30.0+idx*0.1),
                longitude=str(120.0+idx*0.1),
                extra_info={"floor_area_sqm": 100+idx*10, "has_parking": bool(idx%2)},
                status=ShopStatus.OPEN
            ).model_dump())
            # 创建专属图册
            album = await album_repo.create(AlbumCreate(
                name=f"{name}图册",
                description=f"{name}的主图册",
                tags=["门店"],
                status="active",
                sort_order=idx+1,
                meta_data={"scene": "shop"}
            ).model_dump())
            # 关联图册到店铺
            shop.album_id = album.id
            db.add(shop)
            # 添加1~3张随机网络图片
            num_images = random.randint(1, 3)
            cover_image_id = None
            for j in range(num_images):
                url = f"https://picsum.photos/seed/{random.randint(1000,9999)}/800/800"
                created_image = await album_image_repo.create(AlbumImageCreate(
                    album_id=album.id,
                    url=url,
                    file_name=f"shop_{idx+1}_img{j+1}.jpg",
                    file_size=0,
                    width=800,
                    height=800,
                    mime_type="image/jpeg",
                    is_cover=(j==0),
                    sort_order=j+1,
                    status="active",
                    meta_data={"scene": "shop"}
                ).model_dump())
                if j == 0:
                    cover_image_id = created_image.id
            
            # 更新相册的封面ID
            if cover_image_id:
                album.cover_image_id = cover_image_id
                db.add(album)

            created.append(shop)
            logger.info(f"创建门店: {name}")
        await db.commit()
        logger.info(f"成功创建{len(created)}个门店")
        return created
    except Exception as e:
        logger.error(f"创建门店时发生错误: {str(e)}", exc_info=True)
        await db.rollback()
        raise

async def create_inventories(db: AsyncSession, products: list) -> list:
    """为每个商品创建库存"""
    try:
        repo = InventoryRepository(db)
        created = []
        # 商品库存
        for product in products:
            invs, _ = await repo.get_by_product_id(product.id)
            if invs:
                logger.info(f"商品 {product.name} 已有库存，跳过")
                created.extend(invs)
                continue
            inv = InventoryCreate(
                product_id=product.id,
                quantity=product.stock_quantity,
                available_quantity=product.stock_quantity,
                status="available"
            )
            created_inv = await repo.create(inv.model_dump())
            created.append(created_inv)
            logger.info(f"创建商品库存: {product.name}")
        await db.commit()
        logger.info(f"成功创建{len(created)}个库存记录")
        return created
    except Exception as e:
        logger.error(f"创建库存时发生错误: {str(e)}", exc_info=True)
        await db.rollback()
        raise

async def create_album_images(db: AsyncSession, albums: list) -> list:
    """为每个图册创建图片，url随机"""
    try:
        repo = AlbumImageRepository(db)
        created = []
        for album in albums:
            images, _ = await repo.get_album_images(album_id=album.id)
            if images:
                logger.info(f"图册 {album.name} 已有图片，跳过")
                created.extend(images)
                continue
            for i in range(2):
                url = f"https://picsum.photos/seed/{random.randint(1000,9999)}/800/600"
                img = AlbumImageCreate(
                    album_id=album.id,
                    url=url,
                    file_name=f"image_{i+1}.jpg",
                    file_size=102400,
                    width=800,
                    height=600,
                    mime_type="image/jpeg",
                    is_cover=(i==0),
                    sort_order=i+1,
                    status="active",
                    meta_data={}
                )
                created_img = await repo.create(img.model_dump())
                created.append(created_img)
                logger.info(f"为图册 {album.name} 创建图片: {url}")
        await db.commit()
        logger.info(f"成功创建{len(created)}个图册图片")
        return created
    except Exception as e:
        logger.error(f"创建图册图片时发生错误: {str(e)}", exc_info=True)
        await db.rollback()
        raise

async def create_product_specs_and_skus(db: AsyncSession, products: list) -> None:
    """
    为每个商品创建规格、规格值，并生成SKU
    使用最新的产品模块架构
    """
    import random
    from itertools import product as itertools_product

    # 初始化仓库
    spec_repo = SpecRepository(db)
    option_repo = SpecOptionRepository(db)
    sku_repo = ProductSKURepository(db)

    # 全局规格定义（避免重复创建）
    global_specs = [
        {"name": "尺寸", "description": "产品尺寸规格", "sort_order": 1, "options": ["S", "M", "L"]},
        {"name": "颜色", "description": "产品颜色规格", "sort_order": 2, "options": ["黑色", "白色", "红色"]},
        {"name": "材质", "description": "产品材质规格", "sort_order": 3, "options": ["碳纤维", "ABS"]}
    ]

    # 创建全局规格和规格值
    spec_option_map = {}
    for spec_data in global_specs:
        # 检查规格是否已存在
        existing_spec = await spec_repo.get_one(name=spec_data["name"])
        if not existing_spec:
            # 创建规格
            spec_create = SpecCreate(
                name=spec_data["name"],
                description=spec_data["description"],
                sort_order=spec_data["sort_order"],
                status="active"
            )
            spec = await spec_repo.create(spec_create.model_dump())
        else:
            spec = existing_spec

        # 创建规格值
        option_ids = []
        for option_value in spec_data["options"]:
            existing_option = await option_repo.get_one(spec_id=spec.id, value=option_value)
            if not existing_option:
                option_create = SpecOptionCreate(
                    spec_id=spec.id,
                    value=option_value,
                    status="active"
                )
                option = await option_repo.create(option_create.model_dump())
                option_ids.append(option.id)
            else:
                option_ids.append(existing_option.id)

        spec_option_map[spec.id] = option_ids

    # 为每个产品创建SKU
    for product in products:
        # 检查是否已有SKU
        existing_skus = await sku_repo.get_skus_by_product(product.id)
        if existing_skus:
            logger.info(f"商品 {product.name} 已存在SKU，跳过SKU创建")
            continue

        try:
            # 生成所有规格组合的SKU
            option_lists = list(spec_option_map.values())
            skus_created = 0

            for option_combination in itertools_product(*option_lists):
                # 生成SKU编码
                sku_code = f"P{product.id}-{'-'.join(map(str, option_combination))}"

                # 创建SKU
                # 生成SKU图片URL
                image_url = f"https://picsum.photos/400/400?random={product.id}-{skus_created}"
                sku_data = ProductSKUCreate(
                    product_id=product.id,
                    sku=sku_code,
                    price=int(product.price + random.uniform(-1000, 2000)),  # product.price已经是分，直接调整
                    stock_quantity=random.randint(10, 100),
                    status="active",
                    sort_order=skus_created,
                    image_url=image_url,
                    spec_option_ids=list(option_combination)
                )

                await sku_repo.create(sku_data.model_dump())
                skus_created += 1

            logger.info(f"成功为商品 {product.name} 创建 {skus_created} 个SKU")

        except Exception as e:
            logger.error(f"为商品 {product.name} 创建SKU时发生错误: {e}")
            # 继续处理下一个产品，不中断整个流程
            continue

    await db.commit()
    logger.info("产品规格和SKU创建完成")


def get_permissions_data() -> List[Dict[str, str]]:
    """获取权限数据"""
    return [
        # 全局权限
        {"name": "*:*", "description": "所有权限（超级管理员）"},

        # 认证授权模块权限
        {"name": "user:read", "description": "查看用户信息"},
        {"name": "user:create", "description": "创建用户"},
        {"name": "user:update", "description": "更新用户信息"},
        {"name": "user:delete", "description": "删除用户"},
        {"name": "user:manage", "description": "管理用户（禁用/启用账户等）"},
        {"name": "role:read", "description": "查看角色信息"},
        {"name": "role:create", "description": "创建角色"},
        {"name": "role:update", "description": "更新角色"},
        {"name": "role:delete", "description": "删除角色"},
        {"name": "role:assign", "description": "分配角色给用户"},
        {"name": "permission:read", "description": "查看权限信息"},
        {"name": "permission:assign", "description": "给角色分配权限"},

        # 计费/订阅模块权限
        {"name": "subscription_plan:read", "description": "查看订阅计划"},
        {"name": "subscription_plan:create", "description": "创建订阅计划"},
        {"name": "subscription_plan:update", "description": "更新订阅计划"},
        {"name": "subscription_plan:delete", "description": "删除订阅计划"},
        {"name": "subscription:read", "description": "查看订阅信息"},
        {"name": "subscription:create", "description": "创建订阅"},
        {"name": "subscription:update", "description": "更新订阅"},
        {"name": "subscription:cancel", "description": "取消订阅"},
        {"name": "subscription:renew", "description": "续订订阅"},
        {"name": "invoice:read", "description": "查看发票"},
        {"name": "invoice:create", "description": "创建发票"},
        {"name": "invoice:update", "description": "更新发票"},
        {"name": "invoice:delete", "description": "删除发票"},
        {"name": "payment:read", "description": "查看支付信息"},
        {"name": "payment:process", "description": "处理支付"},
        {"name": "payment:refund", "description": "退款操作"},

        # 营销模块权限
        {"name": "campaign:read", "description": "查看营销活动"},
        {"name": "campaign:create", "description": "创建营销活动"},
        {"name": "campaign:update", "description": "更新营销活动"},
        {"name": "campaign:delete", "description": "删除营销活动"},
        {"name": "campaign:activate", "description": "激活/停用营销活动"},
        {"name": "invitation:read", "description": "查看邀请信息"},
        {"name": "invitation:create", "description": "创建邀请"},
        {"name": "invitation:delete", "description": "删除邀请"},
        {"name": "reward_strategy:read", "description": "查看奖励策略"},
        {"name": "reward_strategy:create", "description": "创建奖励策略"},
        {"name": "reward_strategy:update", "description": "更新奖励策略"},
        {"name": "reward_strategy:delete", "description": "删除奖励策略"},
        {"name": "reward_record:read", "description": "查看奖励记录"},
        {"name": "reward_record:create", "description": "创建奖励记录"},
        {"name": "reward_record:issue", "description": "发放奖励"},

        # 产品模块权限
        {"name": "product:read", "description": "查看产品信息"},
        {"name": "product:create", "description": "创建产品"},
        {"name": "product:update", "description": "更新产品"},
        {"name": "product:delete", "description": "删除产品"},
        {"name": "category:read", "description": "查看分类信息"},
        {"name": "category:create", "description": "创建分类"},
        {"name": "category:update", "description": "更新分类"},
        {"name": "category:delete", "description": "删除分类"},
        {"name": "inventory:read", "description": "查看库存信息"},
        {"name": "inventory:update", "description": "更新库存"},
        {"name": "spec:read", "description": "查看规格信息"},
        {"name": "spec:create", "description": "创建规格"},
        {"name": "spec:update", "description": "更新规格"},
        {"name": "spec:delete", "description": "删除规格"},
        {"name": "sku:read", "description": "查看SKU信息"},
        {"name": "sku:create", "description": "创建SKU"},
        {"name": "sku:update", "description": "更新SKU"},
        {"name": "sku:delete", "description": "删除SKU"},

        # 门店模块权限
        {"name": "shop:read", "description": "查看门店信息"},
        {"name": "shop:create", "description": "创建门店"},
        {"name": "shop:update", "description": "更新门店"},
        {"name": "shop:delete", "description": "删除门店"},

        # 图册模块权限
        {"name": "album:read", "description": "查看图册"},
        {"name": "album:create", "description": "创建图册"},
        {"name": "album:update", "description": "更新图册"},
        {"name": "album:delete", "description": "删除图册"},

        # 系统模块权限
        {"name": "config:read", "description": "查看系统配置"},
        {"name": "config:update", "description": "更新系统配置"},
        {"name": "audit_log:read", "description": "查看审计日志"},
        {"name": "audit_log:delete", "description": "删除审计日志"},
        {"name": "system:backup", "description": "系统备份操作"},
        {"name": "system:restore", "description": "系统恢复操作"},
        {"name": "system:maintenance", "description": "系统维护模式切换"},
        {"name": "system:monitor", "description": "系统监控访问权限"}
    ]


def get_roles_data() -> List[Dict[str, Any]]:
    """获取角色数据"""
    return [
        {
            "name": "admin",
            "description": "系统管理员",
            "is_system": True,
            "permissions": ["*:*"]
        },
        {
            "name": "user",
            "description": "普通用户",
            "is_system": True,
            "permissions": [
                "user:read", "product:read", "category:read",
                "album:read", "shop:read"
            ]
        },
        {
            "name": "vip",
            "description": "VIP用户",
            "is_system": True,
            "permissions": [
                "user:read", "product:read", "category:read",
                "album:read", "shop:read", "subscription:read"
            ]
        }
    ]


def get_categories_data() -> List[Dict[str, Any]]:
    """获取分类数据，并为每个分类指定规格及规格值（specs仅用于内存，不入库）"""
    return [
        {
            "name": "安全帽",
            "description": "安全帽分类，提供各种安全帽产品",
            "slug": "safety-helmet",
            # specs字段仅用于后续逻辑，不传入数据库
            "_specs": [
                {"name": "颜色", "options": ["红色", "白色", "黑色"]},
                {"name": "尺寸", "options": ["S", "M", "L"]},
                {"name": "材质", "options": ["ABS", "碳纤维"]},
            ]
        },
        {
            "name": "反光衣",
            "description": "反光衣分类，提供各种反光衣产品",
            "slug": "reflective-vest",
            "_specs": [
                {"name": "颜色", "options": ["黄色", "橙色"]},
                {"name": "尺寸", "options": ["M", "L", "XL"]},
            ]
        }
    ]


async def bulk_create_specs_and_options(db, catid_specs_map):
    """全局去重创建规格，分类与规格多对多关联，返回{category_id: [spec, ...]}"""
    from svc.apps.products.repositories.category import CategoryRepository
    from svc.apps.products.repositories.spec import (SpecOptionRepository,
                                                     SpecRepository)
    from svc.apps.products.schemas.spec import SpecCreate, SpecOptionCreate
    spec_repo = SpecRepository(db)
    option_repo = SpecOptionRepository(db)
    category_repo = CategoryRepository(db)
    category_spec_map = {}
    spec_name_map = {}  # name -> spec对象
    for cat_id, cat_specs in catid_specs_map.items():
        if not cat_specs:
            continue
        category = await category_repo.get_by_id(cat_id)
        if not category:
            continue
        created_specs = []
        for idx, spec_data in enumerate(cat_specs):
            spec_name = spec_data["name"]
            # 先查找全局是否已存在该规格
            if spec_name in spec_name_map:
                spec = spec_name_map[spec_name]
            else:
                spec = await spec_repo.get_one(name=spec_name)
                if not spec:
                    # 新建规格
                    spec = await spec_repo.create(SpecCreate(
                        name=spec_name,
                        description=spec_data.get("description") or f"全局规格：{spec_name}",
                        sort_order=idx+1,
                        is_active=True,
                        options=[]
                    ))
                    # 只为新建的规格创建规格值
                    for opt in spec_data["options"]:
                        await option_repo.create(SpecOptionCreate(
                            spec_id=spec.id,
                            value=opt
                        ))
                spec_name_map[spec_name] = spec
            # 建立多对多关联
            if category not in spec.categories:
                spec.categories.append(category)
                db.add(spec)
            created_specs.append(spec)
        category_spec_map[cat_id] = created_specs
    await db.commit()
    return category_spec_map


async def create_product_skus_by_category(db, products, category_spec_map):
    """为每个产品生成本分类下所有规格组合的SKU"""
    import random
    from itertools import product as iter_product

    sku_repo = ProductSKURepository(db)
    option_repo = SpecOptionRepository(db)
    for prod in products:
        cat_id = prod.category_id
        specs = category_spec_map.get(cat_id, [])
        if not specs:
            continue

        # 检查是否已有SKU
        existing_skus = await sku_repo.get_skus_by_product(prod.id)
        if existing_skus:
            logger.info(f"商品 {prod.name} 已存在SKU，跳过SKU创建")
            continue

        # 获取所有规格的所有option id
        option_lists = []
        for spec in specs:
            # 使用仓库方法获取规格选项，避免懒加载问题
            options = await option_repo.get_options_by_spec(spec.id)
            if options:
                option_ids = [opt.id for opt in options]
                option_lists.append(option_ids)
        if not option_lists:
            continue

        skus_created = 0
        for option_comb in iter_product(*option_lists):
            sku_code = f"P{prod.id}-{'-'.join(map(str, option_comb))}"
            # 生成SKU图片URL
            image_url = f"https://picsum.photos/400/400?random={prod.id}-{skus_created}"
            sku_data = ProductSKUCreate(
                product_id=prod.id,
                sku=sku_code,
                price=int(prod.price + random.randint(-1000, 2000)),  # prod.price已经是分，直接调整
                stock_quantity=random.randint(10, 100),
                status="active",
                sort_order=skus_created,
                image_url=image_url,
                spec_option_ids=list(option_comb)
            )
            await sku_repo.create(sku_data.model_dump())
            skus_created += 1

        logger.info(f"成功为商品 {prod.name} 创建 {skus_created} 个SKU")
    await db.commit()


async def bulk_create_categories_without_specs(seed_service, categories_data):
    """批量创建分类，仅传递Category模型字段，返回(分类对象列表, 分类ID到specs映射)"""
    # 剥离specs字段
    categories_to_create = []
    catid_to_specs = {}
    for cat in categories_data:
        cat_data = {k: v for k, v in cat.items() if not k.endswith('specs')}
        specs = cat.get("_specs")
        categories_to_create.append(cat_data)
        catid_to_specs[cat["slug"]] = specs
    # 批量创建分类
    categories = await seed_service.bulk_create_categories(categories_to_create)
    # 构建slug到category对象映射
    slug_to_cat = {cat.slug: cat for cat in categories}
    # 返回分类对象列表和category_id到specs的映射
    catid_specs_map = {slug_to_cat[slug].id: specs for slug, specs in catid_to_specs.items() if slug in slug_to_cat}
    return categories, catid_specs_map


@performance_monitor("种子数据初始化")
async def main():
    """主函数：初始化所有种子数据（优化版本）"""
    try:
        from scripts.services.seed_data_service import SeedDataService
        from svc.core.database.session import init_engine
        from svc.core.database.session_utils import get_session_for_script
        await init_engine()
        db = await get_session_for_script()
        try:
            logger.info("开始初始化种子数据（使用优化的批量操作）...")
            overall_start_time = time.time()
            seed_service = SeedDataService(db)
            # 1. 分类（含specs仅内存）
            step_start = time.time()
            categories_data = get_categories_data()
            categories, catid_specs_map = await bulk_create_categories_without_specs(seed_service, categories_data)
            logger.info(f"分类批量创建完成，耗时: {time.time() - step_start:.2f} 秒")
            # 2. 规格及规格值
            step_start = time.time()
            category_spec_map = await bulk_create_specs_and_options(db, catid_specs_map)
            logger.info(f"规格及规格值批量创建完成，耗时: {time.time() - step_start:.2f} 秒")
            # 3. 权限和角色
            step_start = time.time()
            permissions_data = get_permissions_data()
            permissions = await seed_service.bulk_create_permissions(permissions_data)
            roles_data = get_roles_data()
            roles = await seed_service.bulk_create_roles(roles_data, permissions)
            logger.info(f"权限和角色创建完成，耗时: {time.time() - step_start:.2f} 秒")
            # 4. 用户
            step_start = time.time()
            users = await create_users(db, roles)
            logger.info(f"用户创建完成，耗时: {time.time() - step_start:.2f} 秒")
            # 5. 订阅计划
            step_start = time.time()
            admin_user_id = users[0].id
            subscription_plans = await create_subscription_plans(db, admin_user_id)
            logger.info(f"订阅计划创建完成，耗时: {time.time() - step_start:.2f} 秒")
            # 6. 营销模块数据
            step_start = time.time()
            campaigns = await create_campaigns(db, admin_user_id)
            reward_strategies = await create_reward_strategies(db, campaigns, admin_user_id)
            invitations = await create_invitations(db, campaigns, users)
            reward_records = await create_reward_records(db, campaigns, users, invitations, reward_strategies)
            logger.info(f"营销模块数据创建完成，耗时: {time.time() - step_start:.2f} 秒")
            # 7. 产品
            step_start = time.time()
            products = await create_products(db, categories)
            logger.info(f"产品创建完成，耗时: {time.time() - step_start:.2f} 秒")
            # 8. 为每个产品生成SKU
            step_start = time.time()
            await create_product_skus_by_category(db, products, category_spec_map)
            logger.info(f"产品SKU创建完成，耗时: {time.time() - step_start:.2f} 秒")
            # 9. 门店
            step_start = time.time()
            await create_shops(db)
            logger.info(f"门店创建完成，耗时: {time.time() - step_start:.2f} 秒")
            # 10. 库存
            step_start = time.time()
            await create_inventories(db, products)
            logger.info(f"库存创建完成，耗时: {time.time() - step_start:.2f} 秒")
            logger.info(f"✅ 所有种子数据初始化完成！总耗时: {time.time() - overall_start_time:.2f} 秒")
        finally:
            await db.close()
    except Exception as e:
        logger.error(f"初始化数据时发生错误: {str(e)}", exc_info=True)
        raise


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="重置数据库并初始化种子数据")
    parser.add_argument("--recreate", action="store_true", help="是否重新创建数据库（dropdb+createdb）")
    parser.add_argument("--reset", action="store_true", help="是否重置数据库（回滚并升级迁移）")
    args = parser.parse_args()

    if args.recreate:
        reset_database(recreate=True)
    elif args.reset:
        reset_database(recreate=False)

    asyncio.run(main())